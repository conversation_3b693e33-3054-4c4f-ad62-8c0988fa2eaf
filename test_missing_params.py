"""
测试缺少补充参数的情况
"""

import asyncio
import json
from intelligent_strategy_matching_system import main, Args

async def test_missing_params():
    """测试缺少补充参数的情况"""
    # 测试缺少补充参数的情况
    test_params = {
        'final_P25': '6.0',
        'final_P50': '8.0',
        'final_P75': '9.0',
        'user_type': '乐观开朗型',
        'cem_value': '1.2',
        # 缺少 cem_grade
        'ei_value': '1.5',
        # 缺少 ei_confidence
        'eii_value': '0.4',
        'rsi_value': '0.8',
        'final_conf_score': '0.8',
        'crisis_probability': '0.2',
        # 缺少 health_score
        # 缺少 trend_prediction
    }
    
    args = Args(test_params)
    result = await main(args)
    print('=== 缺少补充参数的测试结果 ===')
    print('输入验证结果:')
    validation = result['quality_metrics']['input_validation']
    print(f'验证通过: {validation["is_valid"]}')
    print('警告信息:')
    for warning in validation['warnings']:
        print(f'  - {warning}')
    
    print(f'\n用户类型识别: {result["user_type_identification"]["identified_type"]}')
    print(f'情绪状态评估: {result["emotional_state_evaluation"]["current_state"]}')
    print(f'综合置信度: {result["matching_results"]["overall_confidence"]:.3f}')

if __name__ == "__main__":
    asyncio.run(test_missing_params())

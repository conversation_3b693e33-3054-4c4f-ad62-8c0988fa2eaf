"""
1.3 近期情绪画像建立 - 完整功能示例
"""

import asyncio
import json
from datetime import datetime, timedelta
from recent_emotion_profile_builder import main, Args

async def complete_example():
    """完整功能示例"""
    
    print("=" * 70)
    print("1.3 近期情绪画像建立 - 完整功能示例")
    print("=" * 70)
    
    # 构造充足的测试数据（模拟乐观开朗型用户）
    base_time = datetime.now() - timedelta(days=5)
    working_memory = []
    
    # 生成20条记录，模拟乐观开朗型特征
    for i in range(20):
        record_time = base_time + timedelta(hours=i*4, minutes=i*15)
        
        # 乐观开朗型特征：S值6.5-8.5，字数15-50，回复较快
        emo_value = str(6.5 + (i % 5) * 0.4)  # 6.5-8.1范围
        number = str(15 + (i % 8) * 4)        # 15-43字数范围
        
        working_memory.append({
            'emo_value': emo_value,
            'number': number,
            'bstudio_create_time': record_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 模拟长期画像数据
    long_term_profile = {
        'dominant_emotion_type': '乐观开朗型',
        'type_confidence': 0.82,
        'S_P25': 6.8,
        'S_P50': 7.5,
        'S_P75': 8.2
    }
    
    # 模拟质量权重（大部分A/B级）
    quality_weights = ['A'] * 5 + ['B'] * 12 + ['C'] * 3
    
    # 模拟异常标记（大部分正常）
    anomaly_flags = ['normal'] * 18 + ['light'] * 2
    
    # 完整参数测试
    print("\n【完整参数测试】")
    test_params = {
        'working_memory': working_memory,
        'long_term_profile': long_term_profile,
        'quality_weights': quality_weights,
        'anomaly_flags': anomaly_flags
    }
    
    args = Args(test_params)
    result = await main(args)
    
    if result.get('error'):
        print(f"❌ 测试失败: {result.get('error_message')}")
        return
    
    print("✅ 测试成功")
    
    # 显示核心结果
    print(f"\n【核心分析结果】")
    print(f"主导情绪类型: {result.get('dominant_emotion_type')}")
    print(f"类型置信度: {result.get('type_confidence')}")
    print(f"分析置信度: {result.get('analysis_confidence')}")
    print(f"数据数量: {result.get('data_count')}")
    
    # 显示观测基线
    baseline = result.get('observed_baseline', {})
    print(f"\n【观测基线】")
    print(f"P25: {baseline.get('P25')}")
    print(f"P50: {baseline.get('P50')}")
    print(f"P75: {baseline.get('P75')}")
    
    # 显示各类型得分
    print(f"\n【情绪类型得分分布】")
    emotion_scores = result.get('emotion_type_scores', {})
    sorted_scores = sorted(emotion_scores.items(), key=lambda x: x[1], reverse=True)
    for emotion_type, score in sorted_scores:
        print(f"{emotion_type}: {score:.3f}")
    
    # 显示置信度分解
    print(f"\n【置信度分解】")
    confidence_breakdown = result.get('detailed_analysis', {}).get('confidence_breakdown', {})
    print(f"数据充分性: {confidence_breakdown.get('data_sufficiency', 0):.3f}")
    print(f"内部一致性: {confidence_breakdown.get('internal_consistency', 0):.3f}")
    print(f"时间稳定性: {confidence_breakdown.get('time_stability', 0):.3f}")
    print(f"差异度置信: {confidence_breakdown.get('difference_confidence', 0):.3f}")
    
    # 显示质量指标
    print(f"\n【质量控制指标】")
    quality_metrics = result.get('quality_metrics', {})
    print(f"数据充分性等级: {quality_metrics.get('data_sufficiency_level')}")
    print(f"分析可靠性等级: {quality_metrics.get('analysis_reliability')}")
    
    # 显示处理信息
    print(f"\n【数据处理信息】")
    processing_info = result.get('processing_info', {})
    print(f"原始数据数量: {processing_info.get('raw_data_count')}")
    print(f"处理后数据数量: {processing_info.get('processed_data_count')}")
    print(f"总有效权重: {processing_info.get('total_effective_weight')}")
    
    # 显示标准化输出（给1.4节使用）
    print(f"\n【标准化输出（给1.4节贝叶斯更新使用）】")
    standardized_output = {
        "dominant_emotion_type": result.get('dominant_emotion_type'),
        "type_confidence": result.get('type_confidence'),
        "emotion_type_scores": result.get('emotion_type_scores'),
        "observed_baseline": result.get('observed_baseline'),
        "data_count": result.get('data_count'),
        "analysis_confidence": result.get('analysis_confidence')
    }
    print(json.dumps(standardized_output, indent=2, ensure_ascii=False))
    
    print(f"\n【功能验证总结】")
    print("✅ 工作记忆数据解析和预处理")
    print("✅ 加权分位数和统计量计算")
    print("✅ S/M/T参数完整计算")
    print("✅ 五大情绪类型匹配评分")
    print("✅ 多维度置信度评估")
    print("✅ 标准化输出格式")
    print("✅ 质量控制和错误处理")
    print("✅ 符合1.3节方案的所有要求")

if __name__ == "__main__":
    asyncio.run(complete_example())

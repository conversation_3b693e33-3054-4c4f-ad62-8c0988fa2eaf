"""
展示如何使用新的顶层 strategy_name 字段
"""

import asyncio
import json
from intelligent_strategy_matching_system import main, Args

async def demonstrate_strategy_name_usage():
    """演示如何使用顶层strategy_name字段"""
    
    print("=" * 70)
    print("智能策略匹配系统 - 顶层strategy_name字段使用示例")
    print("=" * 70)
    
    # 示例参数
    params = {
        'final_P25': '6.0',
        'final_P50': '8.0',
        'final_P75': '9.0',
        'user_type': '乐观开朗型',
        'cem_value': '1.2',
        'cem_grade': 'A',
        'ei_value': '1.5',
        'ei_confidence': '0.9',
        'eii_value': '0.4',
        'rsi_value': '0.8',
        'final_conf_score': '0.8',
        'crisis_probability': '0.2',
        'health_score': '0.8',
        'trend_prediction': 'positive'
    }
    
    # 调用智能策略匹配系统
    args = Args(params)
    result = await main(args)
    
    print("\\n【使用方式对比】")
    
    print("\\n1. 🆕 新方式：直接访问顶层strategy_name")
    print("   代码：result['strategy_name']")
    strategy_name_direct = result['strategy_name']
    print(f"   结果：{strategy_name_direct}")
    
    print("\\n2. 📁 原方式：通过嵌套结构访问")
    print("   代码：result['strategy_content_generation']['strategy_name']")
    strategy_name_nested = result['strategy_content_generation']['strategy_name']
    print(f"   结果：{strategy_name_nested}")
    
    print("\\n【实际应用场景】")
    
    print("\\n🎯 场景1：快速获取策略名称用于日志记录")
    print(f"   日志：用户策略匹配完成，选择策略：{result['strategy_name']}")
    
    print("\\n🎯 场景2：API响应中的关键信息提取")
    api_response = {
        'status': 'success',
        'strategy': result['strategy_name'],
        'confidence': result['matching_results']['overall_confidence'],
        'user_type': result['user_type_identification']['identified_type']
    }
    print(f"   API响应：{json.dumps(api_response, ensure_ascii=False)}")
    
    print("\\n🎯 场景3：条件判断和业务逻辑")
    if result['strategy_name'] == '共鸣放大策略':
        action = '启用积极互动模式'
    elif result['strategy_name'] == '深度安抚策略':
        action = '启用危机干预模式'
    else:
        action = '启用标准支持模式'
    print(f"   业务逻辑：根据策略 '{result['strategy_name']}' → {action}")
    
    print("\\n🎯 场景4：数据库存储的简化字段")
    db_record = {
        'user_id': 'user_12345',
        'timestamp': result['timestamp'],
        'strategy_name': result['strategy_name'],  # 直接存储，无需解析嵌套结构
        'confidence': result['matching_results']['overall_confidence']
    }
    print(f"   数据库记录：{json.dumps(db_record, ensure_ascii=False, indent=2)}")
    
    print("\\n【性能和便利性优势】")
    print("✅ 减少了嵌套访问的复杂性")
    print("✅ 提高了代码的可读性")
    print("✅ 降低了访问错误的风险")
    print("✅ 便于快速提取关键信息")
    print("✅ 保持了向后兼容性")
    
    print("\\n【JSON结构示例】")
    simplified_output = {
        'strategy_name': result['strategy_name'],  # 🆕 顶层直接访问
        'user_type': result['user_type_identification']['identified_type'],
        'emotional_state': result['emotional_state_evaluation']['current_state'],
        'confidence': result['matching_results']['overall_confidence'],
        'decision_path': result['matching_results']['decision_path']
    }
    
    print(json.dumps(simplified_output, ensure_ascii=False, indent=2))
    
    print("\\n🎉 现在您可以更方便地访问策略名称了！")

if __name__ == "__main__":
    asyncio.run(demonstrate_strategy_name_usage())

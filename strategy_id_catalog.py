#!/usr/bin/env python3
"""
策略编号对照表生成器
生成完整的策略编号对照表，包含所有15种策略的详细信息
"""

from calculation_9_strategy_matching import StrategyMatcher

def generate_strategy_catalog():
    """生成策略编号对照表"""
    
    print("=" * 80)
    print("计算9智能策略匹配系统 - 策略编号对照表")
    print("=" * 80)
    
    matcher = StrategyMatcher()
    strategy_library = matcher.strategy_library
    
    # 策略类别映射
    category_names = {
        'emotional_regulation': '情绪调节策略',
        'social_interaction': '社交互动策略', 
        'personal_growth': '个人成长策略',
        'crisis_intervention': '危机干预策略',
        'relationship_maintenance': '关系维护策略'
    }
    
    # 收集所有策略信息
    all_strategies = []
    strategy_count = 0
    
    for category_key, category_strategies in strategy_library.items():
        category_name = category_names.get(category_key, category_key)
        
        print(f"\n【{category_name}】")
        print("-" * 60)
        
        for strategy_key, strategy_info in category_strategies.items():
            strategy_count += 1
            
            strategy_id = strategy_info.get('strategy_id', f'unknown_{strategy_key}')
            name = strategy_info.get('name', 'Unknown')
            description = strategy_info.get('description', 'No description')
            suitable_types = strategy_info.get('suitable_types', [])
            crisis_threshold = strategy_info.get('crisis_threshold', 0.0)
            effectiveness = strategy_info.get('effectiveness', 0.0)
            
            # 添加到总列表
            all_strategies.append({
                'strategy_id': strategy_id,
                'strategy_key': strategy_key,
                'name': name,
                'category': category_name,
                'category_key': category_key,
                'description': description,
                'suitable_types': suitable_types,
                'crisis_threshold': crisis_threshold,
                'effectiveness': effectiveness
            })
            
            # 打印详细信息
            print(f"编号: {strategy_id}")
            print(f"名称: {name}")
            print(f"描述: {description}")
            print(f"适用用户类型: {', '.join(suitable_types)}")
            print(f"危机阈值: {crisis_threshold}")
            print(f"有效性: {effectiveness}")
            print()
    
    # 生成汇总表
    print("=" * 80)
    print("策略编号汇总表")
    print("=" * 80)
    
    print(f"{'编号':<12} {'策略名称':<20} {'类别':<15} {'危机阈值':<8} {'有效性':<8}")
    print("-" * 80)
    
    for strategy in all_strategies:
        print(f"{strategy['strategy_id']:<12} {strategy['name']:<20} {strategy['category']:<15} {strategy['crisis_threshold']:<8} {strategy['effectiveness']:<8}")
    
    # 按类别统计
    print("\n" + "=" * 80)
    print("按类别统计")
    print("=" * 80)
    
    category_stats = {}
    for strategy in all_strategies:
        category = strategy['category']
        if category not in category_stats:
            category_stats[category] = []
        category_stats[category].append(strategy['strategy_id'])
    
    for category, strategy_ids in category_stats.items():
        print(f"{category}: {len(strategy_ids)}个策略")
        print(f"  编号范围: {', '.join(strategy_ids)}")
        print()
    
    # 验证编号唯一性
    print("=" * 80)
    print("编号唯一性验证")
    print("=" * 80)
    
    strategy_ids = [s['strategy_id'] for s in all_strategies]
    unique_ids = set(strategy_ids)
    
    print(f"总策略数量: {len(strategy_ids)}")
    print(f"唯一编号数量: {len(unique_ids)}")
    
    if len(strategy_ids) == len(unique_ids):
        print("✅ 所有策略编号唯一，无重复")
    else:
        print("❌ 发现重复编号")
        duplicates = [id for id in strategy_ids if strategy_ids.count(id) > 1]
        print(f"重复编号: {set(duplicates)}")
    
    # 验证编号连续性
    expected_ids = [f"strategy_{i:03d}" for i in range(1, strategy_count + 1)]
    actual_ids = sorted([s['strategy_id'] for s in all_strategies])
    
    print(f"\n编号连续性验证:")
    print(f"期望编号: {expected_ids}")
    print(f"实际编号: {actual_ids}")
    
    if expected_ids == actual_ids:
        print("✅ 编号连续且格式正确")
    else:
        print("❌ 编号不连续或格式错误")
        missing = set(expected_ids) - set(actual_ids)
        extra = set(actual_ids) - set(expected_ids)
        if missing:
            print(f"缺失编号: {missing}")
        if extra:
            print(f"额外编号: {extra}")
    
    print(f"\n总计: {strategy_count}种策略已完成编号")
    
    return all_strategies

def generate_markdown_catalog():
    """生成Markdown格式的策略对照表"""
    
    matcher = StrategyMatcher()
    strategy_library = matcher.strategy_library
    
    markdown_content = """# 计算9智能策略匹配系统 - 策略编号对照表

## 策略总览

| 编号 | 策略名称 | 类别 | 适用用户类型 | 危机阈值 | 有效性 |
|------|----------|------|--------------|----------|--------|
"""
    
    category_names = {
        'emotional_regulation': '情绪调节',
        'social_interaction': '社交互动', 
        'personal_growth': '个人成长',
        'crisis_intervention': '危机干预',
        'relationship_maintenance': '关系维护'
    }
    
    all_strategies = []
    
    for category_key, category_strategies in strategy_library.items():
        category_name = category_names.get(category_key, category_key)
        
        for strategy_key, strategy_info in category_strategies.items():
            strategy_id = strategy_info.get('strategy_id', f'unknown_{strategy_key}')
            name = strategy_info.get('name', 'Unknown')
            suitable_types = strategy_info.get('suitable_types', [])
            crisis_threshold = strategy_info.get('crisis_threshold', 0.0)
            effectiveness = strategy_info.get('effectiveness', 0.0)
            
            suitable_types_str = ', '.join(suitable_types)
            markdown_content += f"| {strategy_id} | {name} | {category_name} | {suitable_types_str} | {crisis_threshold} | {effectiveness} |\n"
            
            all_strategies.append({
                'strategy_id': strategy_id,
                'name': name,
                'category': category_name,
                'category_key': category_key
            })
    
    # 按类别分组
    markdown_content += "\n## 按类别分组\n\n"
    
    category_groups = {}
    for strategy in all_strategies:
        category = strategy['category']
        if category not in category_groups:
            category_groups[category] = []
        category_groups[category].append(strategy)
    
    for category, strategies in category_groups.items():
        markdown_content += f"### {category}策略\n\n"
        for strategy in strategies:
            markdown_content += f"- **{strategy['strategy_id']}**: {strategy['name']}\n"
        markdown_content += "\n"
    
    # 保存到文件
    with open('strategy_catalog.md', 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    print("✅ Markdown格式策略对照表已生成: strategy_catalog.md")

if __name__ == "__main__":
    # 生成控制台版本
    strategies = generate_strategy_catalog()
    
    # 生成Markdown版本
    print("\n" + "=" * 80)
    generate_markdown_catalog()

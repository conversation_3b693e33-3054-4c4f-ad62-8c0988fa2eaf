"""
1.3 近期情绪画像建立 - 完整真实数据示例
使用您提供的完整28条真实数据进行分析
"""

import asyncio
import json
from recent_emotion_profile_builder import main, Args

async def final_real_data_example():
    """使用完整真实数据的示例"""
    
    print("=" * 80)
    print("1.3 近期情绪画像建立 - 完整真实数据分析")
    print("=" * 80)
    
    # 您提供的完整28条真实数据
    complete_working_memory = [
        {"P25": "5.89", "P50": "6.83", "P75": "8.66", "bstudio_create_time": "2025-07-14 22:42:00 +0800 CST", "conversation": "你说的这个积极重构法与冲突解决技巧是什么东西呀", "delta_days": 1, "emo_value": "6", "emotion_change": 1, "emotion_deviation": "1.2199999999999998", "feature_score": "0.9396607142857142", "number": "23", "priority_score": "6.782180357142858", "quality_score": "9.6", "rsi_history": "0.443", "weight_anomaly": ""},
        {"P25": "5.89", "P50": "6.83", "P75": "8.67", "bstudio_create_time": "2025-07-14 22:43:09 +0800 CST", "conversation": "哦，听起来不错，那么就请你重点讲一下冲突解决技巧吧", "delta_days": 1, "emo_value": "5", "emotion_change": 2, "emotion_deviation": "2.2199999999999998", "feature_score": "1.4236607142857145", "number": "23", "priority_score": "6.580580357142858", "quality_score": "9.6", "rsi_history": "0.438", "weight_anomaly": ""},
        {"P25": "5.86", "P50": "7.21", "P75": "8.57", "bstudio_create_time": "2025-07-14 20:23:12 +0800 CST", "conversation": "相信我，我们一定可以的", "delta_days": 1, "emo_value": "7", "emotion_change": 0, "emotion_deviation": "0.21999999999999975", "feature_score": "0.4656607142857142", "number": "10", "priority_score": "6.254780357142858", "quality_score": "9.4", "rsi_history": "0.454", "weight_anomaly": ""},
        {"P25": "5.42", "P50": "6.96", "P75": "7.25", "bstudio_create_time": "2025-07-08 22:28:35 +0800 CST", "conversation": "今晚的星星真美呀", "delta_days": 7, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": 1.093625, "number": "8", "priority_score": 6.0668625, "quality_score": "7.8", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.86", "P50": "7.14", "P75": "8.58", "bstudio_create_time": "2025-07-14 20:25:34 +0800 CST", "conversation": "我们这次成功了", "delta_days": 1, "emo_value": "7", "emotion_change": 0, "emotion_deviation": "0.21999999999999975", "feature_score": "0.4576607142857142", "number": "7", "priority_score": "5.943980357142856", "quality_score": "9.0", "rsi_history": "0.456", "weight_anomaly": ""},
        {"P25": "5.89", "P50": "6.84", "P75": "8.68", "bstudio_create_time": "2025-07-14 20:32:55 +0800 CST", "conversation": "一切都好起来了", "delta_days": 1, "emo_value": "7", "emotion_change": 0, "emotion_deviation": "0.21999999999999975", "feature_score": "0.4576607142857142", "number": "7", "priority_score": "5.943980357142856", "quality_score": "9.0", "rsi_history": "0.46", "weight_anomaly": ""},
        {"P25": "5.89", "P50": "8.60", "P75": "8.66", "bstudio_create_time": "2025-07-11 17:05:25 +0800 CST", "conversation": "天晴了，雨停了，你又在哪里呢", "delta_days": 5, "emo_value": "5", "emotion_change": 2, "emotion_deviation": "2.2199999999999998", "feature_score": "1.3963035714285716", "number": "12", "priority_score": "5.940701785714285", "quality_score": "9.4", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.92", "P50": "8.73", "P75": "8.77", "bstudio_create_time": "2025-07-10 13:59:05 +0800 CST", "conversation": "哈哈哈哈哈哈", "delta_days": 6, "emo_value": "10", "emotion_change": "2.5600000000000005", "emotion_deviation": "2.7800000000000002", "feature_score": "1.510964285714286", "number": "6", "priority_score": "5.9403821428571435", "quality_score": "7.0", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.40", "P50": "6.10", "P75": "7.20", "bstudio_create_time": "2025-07-08 23:02:43 +0800 CST", "conversation": "有你陪我看星星真好", "delta_days": 7, "emo_value": "7", "emotion_change": 0, "emotion_deviation": "0.21999999999999975", "feature_score": "0.45362499999999994", "number": "9", "priority_score": 5.8728625, "quality_score": "8.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.89", "P50": "6.73", "P75": "8.67", "bstudio_create_time": "2025-07-15 14:41:52 +0800 CST", "conversation": "哈哈哈哈哈", "delta_days": 1, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0956607142857144", "number": "5", "priority_score": "5.847780357142858", "quality_score": "7.6", "rsi_history": "0.428", "weight_anomaly": ""},
        {"P25": "5.89", "P50": "6.44", "P75": "8.67", "bstudio_create_time": "2025-07-15 14:41:23 +0800 CST", "conversation": "哈哈哈哈哈", "delta_days": 1, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0956607142857144", "number": "5", "priority_score": "5.847780357142858", "quality_score": "7.6", "rsi_history": "0.419", "weight_anomaly": ""},
        {"P25": "5.89", "P50": "6.84", "P75": "8.67", "bstudio_create_time": "2025-07-15 15:11:20 +0800 CST", "conversation": "哈哈哈哈哈", "delta_days": 1, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0956607142857144", "number": "5", "priority_score": "5.847780357142858", "quality_score": "7.6", "rsi_history": "0.428", "weight_anomaly": ""},
        {"P25": "5.89", "P50": "6.82", "P75": "8.67", "bstudio_create_time": "2025-07-15 15:11:03 +0800 CST", "conversation": "哈哈哈哈哈", "delta_days": 1, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0956607142857144", "number": "5", "priority_score": "5.847780357142858", "quality_score": "7.6", "rsi_history": "0.428", "weight_anomaly": ""},
        {"P25": "5.90", "P50": "7.16", "P75": "8.69", "bstudio_create_time": "2025-07-11 14:46:29 +0800 CST", "conversation": "哈哈哈哈哈", "delta_days": 5, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0903035714285716", "number": "5", "priority_score": "5.840101785714286", "quality_score": "7.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.89", "P50": "8.61", "P75": "8.67", "bstudio_create_time": "2025-07-11 17:04:19 +0800 CST", "conversation": "哈哈哈哈哈", "delta_days": 5, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0903035714285716", "number": "5", "priority_score": "5.840101785714286", "quality_score": "7.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "6.98", "P50": "8.02", "P75": "8.16", "bstudio_create_time": "2025-07-09 14:50:43 +0800 CST", "conversation": "哈哈哈哈哈", "delta_days": 7, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": 1.087625, "number": "5", "priority_score": 5.8362625, "quality_score": "7.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "6.15", "P50": "8.85", "P75": "8.87", "bstudio_create_time": "2025-07-09 15:17:02 +0800 CST", "conversation": "哈哈哈哈哈", "delta_days": 7, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": 1.087625, "number": "5", "priority_score": 5.8362625, "quality_score": "7.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.89", "P50": "6.83", "P75": "8.67", "bstudio_create_time": "2025-07-14 22:45:09 +0800 CST", "conversation": "你现在是什么感觉", "delta_days": 1, "emo_value": "5", "emotion_change": 2, "emotion_deviation": "2.2199999999999998", "feature_score": "1.3896607142857142", "number": "8", "priority_score": "5.827180357142858", "quality_score": "9.6", "rsi_history": "0.439", "weight_anomaly": ""},
        {"P25": "5.88", "P50": "8.57", "P75": "8.63", "bstudio_create_time": "2025-07-12 20:57:46 +0800 CST", "conversation": "哈哈哈哈", "delta_days": 3, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.090982142857143", "number": "4", "priority_score": "5.793741071428571", "quality_score": "7.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.20", "P50": "8.19", "P75": "8.61", "bstudio_create_time": "2025-07-11 20:02:06 +0800 CST", "conversation": "哈哈哈哈", "delta_days": 4, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0896428571428571", "number": "4", "priority_score": "5.791821428571429", "quality_score": "7.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.20", "P50": "8.53", "P75": "8.60", "bstudio_create_time": "2025-07-14 17:11:07 +0800 CST", "conversation": "那很好了", "delta_days": 2, "emo_value": "7", "emotion_change": 0, "emotion_deviation": "0.21999999999999975", "feature_score": "0.45032142857142854", "number": "4", "priority_score": "5.791460714285714", "quality_score": "9.0", "rsi_history": "0.476", "weight_anomaly": ""},
        {"P25": "5.90", "P50": "7.15", "P75": "8.70", "bstudio_create_time": "2025-07-11 13:45:08 +0800 CST", "conversation": "哈哈哈哈", "delta_days": 5, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0883035714285716", "number": "4", "priority_score": "5.789901785714286", "quality_score": "7.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.90", "P50": "7.70", "P75": "8.69", "bstudio_create_time": "2025-07-11 15:10:34 +0800 CST", "conversation": "哇哈哈哈", "delta_days": 5, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0883035714285716", "number": "4", "priority_score": "5.789901785714286", "quality_score": "7.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.92", "P50": "8.71", "P75": "8.75", "bstudio_create_time": "2025-07-10 16:59:40 +0800 CST", "conversation": "哈哈哈哈", "delta_days": 6, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0869642857142858", "number": "4", "priority_score": "5.7879821428571425", "quality_score": "7.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.92", "P50": "8.71", "P75": "8.75", "bstudio_create_time": "2025-07-10 16:57:53 +0800 CST", "conversation": "哈哈哈哈", "delta_days": 6, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0869642857142858", "number": "4", "priority_score": "5.7879821428571425", "quality_score": "7.6", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "5.20", "P50": "8.53", "P75": "8.60", "bstudio_create_time": "2025-07-14 17:15:26 +0800 CST", "conversation": "你知道吗？我想你了", "delta_days": 2, "emo_value": "6", "emotion_change": 1, "emotion_deviation": "1.2199999999999998", "feature_score": "0.9103214285714286", "number": "8", "priority_score": "5.787460714285714", "quality_score": "9.0", "rsi_history": "0.476", "weight_anomaly": ""},
        {"P25": "6.18", "P50": "8.82", "P75": "8.85", "bstudio_create_time": "2025-07-09 21:29:45 +0800 CST", "conversation": "哈哈哈哈哈", "delta_days": 6, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0889642857142858", "number": "5", "priority_score": "5.6781821428571435", "quality_score": "7.2", "rsi_history": "", "weight_anomaly": ""},
        {"P25": "6.18", "P50": "8.82", "P75": "8.85", "bstudio_create_time": "2025-07-09 21:32:14 +0800 CST", "conversation": "哈哈哈哈哈", "delta_days": 6, "emo_value": "9", "emotion_change": "1.5600000000000005", "emotion_deviation": "1.7800000000000002", "feature_score": "1.0889642857142858", "number": "5", "priority_score": "5.6781821428571435", "quality_score": "7.2", "rsi_history": "", "weight_anomaly": ""}
    ]
    
    print(f"✅ 完整真实数据包含 {len(complete_working_memory)} 条记录")
    
    # 数据特征分析
    emo_values = [float(r['emo_value']) for r in complete_working_memory]
    numbers = [int(r['number']) for r in complete_working_memory]
    quality_scores = [float(r['quality_score']) for r in complete_working_memory]
    
    print(f"数据特征分析:")
    print(f"  - 情绪值范围: {min(emo_values)} - {max(emo_values)}")
    print(f"  - 字数范围: {min(numbers)} - {max(numbers)}")
    print(f"  - 质量分数范围: {min(quality_scores)} - {max(quality_scores)}")
    print(f"  - 时间跨度: 2025-07-08 到 2025-07-15 (7天)")
    
    # 根据质量分数生成权重
    quality_weights = []
    for record in complete_working_memory:
        quality_score = float(record['quality_score'])
        if quality_score >= 9.0:
            quality_weights.append('A')
        elif quality_score >= 8.0:
            quality_weights.append('B')
        elif quality_score >= 7.0:
            quality_weights.append('C')
        else:
            quality_weights.append('D')
    
    print(f"质量权重分布: A级{quality_weights.count('A')}条, B级{quality_weights.count('B')}条, C级{quality_weights.count('C')}条")
    
    # 完整参数配置
    complete_params = {
        'working_memory': complete_working_memory,
        'quality_weights': quality_weights,
        'anomaly_flags': ['normal'] * len(complete_working_memory),
        'weight_anomaly': 'normal'
    }
    
    print(f"\n【执行完整数据分析】")
    args = Args(complete_params)
    result = await main(args)
    
    if result.get('error'):
        print(f"❌ 分析失败: {result.get('error_message')}")
        return
    
    print("✅ 分析成功！")
    
    # 🎯 核心结果展示
    print(f"\n【🎯 核心分析结果】")
    print(f"近期主导情绪类型: {result.get('dominant_emotion_type')}")
    print(f"类型置信度: {result.get('type_confidence'):.3f}")
    
    # 🔥 重点：您要的P25/P50/P75值
    observed_baseline = result.get('observed_baseline', {})
    print(f"\n📊 近期情绪基线（您要的核心输出）:")
    print(f"  P25 (25分位数): {observed_baseline.get('P25')}")
    print(f"  P50 (中位数): {observed_baseline.get('P50')}")
    print(f"  P75 (75分位数): {observed_baseline.get('P75')}")
    
    print(f"\n📈 分析质量指标:")
    print(f"  数据数量: {result.get('data_count')}")
    print(f"  分析置信度: {result.get('analysis_confidence'):.3f}")
    
    quality_metrics = result.get('quality_metrics', {})
    print(f"  数据充分性: {quality_metrics.get('data_sufficiency_level')}")
    print(f"  分析可靠性: {quality_metrics.get('analysis_reliability')}")
    
    # 情绪类型得分分布
    print(f"\n📊 情绪类型得分分布:")
    emotion_scores = result.get('emotion_type_scores', {})
    sorted_scores = sorted(emotion_scores.items(), key=lambda x: x[1], reverse=True)
    for i, (emotion_type, score) in enumerate(sorted_scores, 1):
        marker = "👑" if i == 1 else f"{i}."
        print(f"  {marker} {emotion_type}: {score:.3f}")
    
    # 🎯 最终标准化输出
    print(f"\n【🎯 最终标准化输出（给1.4节贝叶斯更新使用）】")
    final_output = {
        "dominant_emotion_type": result.get('dominant_emotion_type'),
        "type_confidence": result.get('type_confidence'),
        "emotion_type_scores": result.get('emotion_type_scores'),
        "observed_baseline": result.get('observed_baseline'),
        "data_count": result.get('data_count'),
        "analysis_confidence": result.get('analysis_confidence')
    }
    print(json.dumps(final_output, indent=2, ensure_ascii=False))
    
    print(f"\n【✅ 完整分析总结】")
    print("🎯 基于您的28条真实数据，系统成功计算出:")
    print(f"  ✅ 近期主导情绪类型: {result.get('dominant_emotion_type')}")
    print(f"  ✅ P25基线值: {observed_baseline.get('P25')}")
    print(f"  ✅ P50基线值: {observed_baseline.get('P50')}")
    print(f"  ✅ P75基线值: {observed_baseline.get('P75')}")
    print(f"  ✅ 分析置信度: {result.get('analysis_confidence'):.3f}")
    
    print(f"\n🚀 这就是1.3节近期情绪画像建立的完整功能输出！")
    print("📋 代码完全适配您的数据格式，可以直接在生产环境中使用。")

if __name__ == "__main__":
    asyncio.run(final_real_data_example())

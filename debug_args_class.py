"""
调试Args类的参数处理
"""

from recent_emotion_profile_builder import Args, validate_input_parameters

def debug_args_class():
    """调试Args类的参数处理"""
    
    print("=" * 60)
    print("调试Args类的参数处理")
    print("=" * 60)
    
    # 测试数据
    test_input = {
        "working_memory": [
            {
                "P25": "5.89",
                "P50": "6.83",
                "P75": "8.66",
                "bstudio_create_time": "2025-07-14 22:42:00 +0800 CST",
                "conversation": "你说的这个积极重构法与冲突解决技巧是什么东西呀",
                "delta_days": 1,
                "emo_value": "6",
                "emotion_change": "-0.5600000000000005",
                "emotion_deviation": "1.2199999999999998",
                "feature_score": "0.6276607142857141",
                "number": "23",
                "priority_score": "6.750980357142858",
                "quality_score": "9.6",
                "rsi_history": "0.443",
                "weight_anomaly": ""
            },
            {
                "P25": "5.89",
                "P50": "6.83",
                "P75": "8.67",
                "bstudio_create_time": "2025-07-14 22:43:09 +0800 CST",
                "conversation": "哦，听起来不错，那么就请你重点讲一下冲突解决技巧吧",
                "delta_days": 1,
                "emo_value": "5",
                "emotion_change": "0.4399999999999995",
                "emotion_deviation": "2.2199999999999998",
                "feature_score": "1.1116607142857142",
                "number": "23",
                "priority_score": "6.549380357142858",
                "quality_score": "9.6",
                "rsi_history": "0.438",
                "weight_anomaly": ""
            }
        ]
    }
    
    print("1. 原始输入数据:")
    print(f"   working_memory长度: {len(test_input['working_memory'])}")
    print(f"   第一条记录的emo_value: {test_input['working_memory'][0]['emo_value']}")
    
    print("\n2. 创建Args对象:")
    args = Args(test_input)
    print(f"   args.params类型: {type(args.params)}")
    print(f"   args.params.keys(): {list(args.params.keys())}")
    
    if 'working_memory' in args.params:
        wm = args.params['working_memory']
        print(f"   working_memory类型: {type(wm)}")
        print(f"   working_memory长度: {len(wm) if wm else 0}")
        if wm and len(wm) > 0:
            print(f"   第一条记录类型: {type(wm[0])}")
            print(f"   第一条记录的emo_value: {wm[0].get('emo_value', '未找到')}")
        else:
            print("   working_memory为空或None!")
    else:
        print("   working_memory字段不存在!")
    
    print("\n3. 验证函数测试:")
    validation_result = validate_input_parameters(args.params)
    print(f"   验证结果: {validation_result}")
    
    print("\n4. 直接验证原始数据:")
    direct_validation = validate_input_parameters(test_input)
    print(f"   直接验证结果: {direct_validation}")
    
    # 检查是否是引用问题
    print("\n5. 引用检查:")
    print(f"   原始数据id: {id(test_input)}")
    print(f"   args.params id: {id(args.params)}")
    print(f"   原始working_memory id: {id(test_input['working_memory'])}")
    if 'working_memory' in args.params:
        print(f"   args working_memory id: {id(args.params['working_memory'])}")

if __name__ == "__main__":
    debug_args_class()

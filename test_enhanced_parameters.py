"""
测试新增输入参数的功能增强效果
"""

import asyncio
import json
from datetime import datetime, timedelta
from recent_emotion_profile_builder import main, Args

async def test_enhanced_parameters():
    """测试新增参数的功能增强效果"""
    
    print("=" * 70)
    print("1.3 近期情绪画像建立 - 新增参数功能测试")
    print("=" * 70)
    
    # 构造测试数据
    base_time = datetime.now() - timedelta(days=3)
    working_memory = []
    
    # 生成15条记录，模拟乐观开朗型特征
    for i in range(15):
        record_time = base_time + timedelta(hours=i*4, minutes=i*20)
        emo_value = str(6.8 + (i % 4) * 0.3)  # 6.8-7.7范围
        number = str(18 + (i % 6) * 3)        # 18-33字数范围
        
        working_memory.append({
            'emo_value': emo_value,
            'number': number,
            'bstudio_create_time': record_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 测试用例1：基础参数（不包含新增参数）
    print("\n【测试用例1：基础参数（对照组）】")
    basic_params = {
        'working_memory': working_memory
    }
    
    args1 = Args(basic_params)
    result1 = await main(args1)
    
    print(f"主导情绪类型: {result1.get('dominant_emotion_type')}")
    print(f"类型置信度: {result1.get('type_confidence')}")
    print(f"分析置信度: {result1.get('analysis_confidence')}")
    print(f"验证警告数量: {len(result1['quality_metrics']['input_validation']['warnings'])}")
    
    # 显示置信度分解
    confidence_breakdown = result1.get('detailed_analysis', {}).get('confidence_breakdown', {})
    print("置信度分解:")
    for key, value in confidence_breakdown.items():
        print(f"  {key}: {value:.3f}")
    
    # 测试用例2：完整新增参数
    print("\n【测试用例2：包含所有新增参数（实验组）】")
    enhanced_params = {
        'working_memory': working_memory,
        'user_type': '乐观开朗型',           # 新增：用户类型
        'P25': '6.5',                      # 新增：长期基线P25
        'P50': '7.2',                      # 新增：长期基线P50
        'P75': '7.8',                      # 新增：长期基线P75
        'weight_anomaly': 'normal',        # 新增：权重异常标记
        'quality_weights': ['A'] * 10 + ['B'] * 5,
        'anomaly_flags': ['normal'] * 15
    }
    
    args2 = Args(enhanced_params)
    result2 = await main(args2)
    
    print(f"主导情绪类型: {result2.get('dominant_emotion_type')}")
    print(f"类型置信度: {result2.get('type_confidence')}")
    print(f"分析置信度: {result2.get('analysis_confidence')}")
    print(f"验证警告数量: {len(result2['quality_metrics']['input_validation']['warnings'])}")
    
    # 显示置信度分解
    confidence_breakdown2 = result2.get('detailed_analysis', {}).get('confidence_breakdown', {})
    print("置信度分解:")
    for key, value in confidence_breakdown2.items():
        print(f"  {key}: {value:.3f}")
    
    # 显示新增参数使用情况
    enhanced_info = result2.get('processing_info', {}).get('enhanced_parameters', {})
    print("新增参数使用情况:")
    print(f"  用户类型已提供: {enhanced_info.get('user_type_provided')}")
    print(f"  长期基线已提供: {enhanced_info.get('long_term_baseline_provided')}")
    print(f"  权重异常已应用: {enhanced_info.get('weight_anomaly_applied')}")
    print(f"  用户类型值: {enhanced_info.get('user_type_value')}")
    print(f"  权重异常级别: {enhanced_info.get('weight_anomaly_level')}")
    
    # 测试用例3：权重异常影响测试
    print("\n【测试用例3：权重异常影响测试】")
    anomaly_params = {
        'working_memory': working_memory,
        'user_type': '乐观开朗型',
        'P25': '6.5',
        'P50': '7.2',
        'P75': '7.8',
        'weight_anomaly': 'light',         # 轻微异常
        'quality_weights': ['A'] * 10 + ['B'] * 5,
        'anomaly_flags': ['normal'] * 15
    }
    
    args3 = Args(anomaly_params)
    result3 = await main(args3)
    
    print(f"权重异常级别: light")
    print(f"分析置信度: {result3.get('analysis_confidence')}")
    print(f"总有效权重: {result3.get('processing_info', {}).get('total_effective_weight')}")
    
    # 效果对比分析
    print("\n【效果对比分析】")
    print(f"分析置信度提升: {result2.get('analysis_confidence') - result1.get('analysis_confidence'):.3f}")
    print(f"验证警告减少: {len(result1['quality_metrics']['input_validation']['warnings']) - len(result2['quality_metrics']['input_validation']['warnings'])} 个")
    
    # 新增置信度维度对比
    if 'type_consistency' in confidence_breakdown2:
        print(f"新增类型一致性置信度: {confidence_breakdown2.get('type_consistency', 0):.3f}")
    if 'baseline_consistency' in confidence_breakdown2:
        print(f"新增基线一致性置信度: {confidence_breakdown2.get('baseline_consistency', 0):.3f}")
    
    # 权重异常影响分析
    weight_diff = result2.get('processing_info', {}).get('total_effective_weight', 0) - result3.get('processing_info', {}).get('total_effective_weight', 0)
    print(f"权重异常对总权重的影响: -{weight_diff:.3f}")
    
    print("\n【新增参数功能验证总结】")
    print("✅ user_type参数：提升类型一致性评估精度")
    print("✅ P25/P50/P75参数：增加基线一致性评估维度")
    print("✅ weight_anomaly参数：支持全局权重调整")
    print("✅ 多维度置信度评估：更全面的可靠性分析")
    print("✅ 增强的输入验证：更详细的参数检查")
    print("✅ 处理信息追踪：完整的参数使用记录")
    
    # 输出增强后的标准化结果
    print("\n【增强版标准化输出示例】")
    enhanced_output = {
        "dominant_emotion_type": result2.get('dominant_emotion_type'),
        "type_confidence": result2.get('type_confidence'),
        "emotion_type_scores": result2.get('emotion_type_scores'),
        "observed_baseline": result2.get('observed_baseline'),
        "data_count": result2.get('data_count'),
        "analysis_confidence": result2.get('analysis_confidence'),
        "enhanced_metrics": {
            "type_consistency": confidence_breakdown2.get('type_consistency'),
            "baseline_consistency": confidence_breakdown2.get('baseline_consistency'),
            "parameter_completeness": enhanced_info.get('user_type_provided', False) and enhanced_info.get('long_term_baseline_provided', False)
        }
    }
    print(json.dumps(enhanced_output, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    asyncio.run(test_enhanced_parameters())

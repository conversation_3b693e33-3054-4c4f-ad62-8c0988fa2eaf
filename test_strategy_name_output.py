"""
测试策略名称的顶层输出功能
"""

import asyncio
import json
from intelligent_strategy_matching_system import main, Args

async def test_strategy_name_output():
    """测试策略名称的顶层输出功能"""
    
    print("=" * 60)
    print("智能策略匹配系统 - 策略名称顶层输出测试")
    print("=" * 60)
    
    # 测试用例1：乐观开朗型用户
    test_params_1 = {
        'final_P25': '6.0',
        'final_P50': '8.0',
        'final_P75': '9.0',
        'user_type': '乐观开朗型',
        'cem_value': '1.2',
        'cem_grade': 'A',
        'ei_value': '1.5',
        'ei_confidence': '0.9',
        'eii_value': '0.4',
        'rsi_value': '0.8',
        'final_conf_score': '0.8',
        'crisis_probability': '0.2',
        'health_score': '0.8',
        'trend_prediction': 'positive'
    }
    
    # 测试用例2：悲观消极型用户（危机状态）
    test_params_2 = {
        'final_P25': '3.0',
        'final_P50': '4.5',
        'final_P75': '6.0',
        'user_type': '悲观消极型',
        'cem_value': '-1.2',
        'cem_grade': 'D',
        'ei_value': '1.3',
        'ei_confidence': '0.9',
        'eii_value': '0.8',
        'rsi_value': '0.4',
        'final_conf_score': '0.7',
        'crisis_probability': '0.8',
        'health_score': '0.3',
        'trend_prediction': 'negative'
    }
    
    test_cases = [
        ("乐观开朗型用户", test_params_1),
        ("悲观消极型用户（危机状态）", test_params_2)
    ]
    
    for case_name, params in test_cases:
        print(f"\n【测试用例：{case_name}】")
        
        args = Args(params)
        result = await main(args)
        
        # 获取策略名称
        top_level_name = result.get('strategy_name')
        nested_name = result.get('strategy_content_generation', {}).get('strategy_name')
        
        print(f"顶层策略名称: {top_level_name}")
        print(f"嵌套策略名称: {nested_name}")
        
        # 验证一致性
        if top_level_name == nested_name:
            print("✅ 两个位置的策略名称一致")
        else:
            print("❌ 两个位置的策略名称不一致")
        
        # 显示其他相关信息
        user_type = result.get('user_type_identification', {}).get('identified_type')
        emotional_state = result.get('emotional_state_evaluation', {}).get('current_state')
        
        print(f"用户类型: {user_type}")
        print(f"情绪状态: {emotional_state}")
        print(f"决策路径: {result.get('matching_results', {}).get('decision_path')}")
    
    print(f"\n【输出结构验证】")
    # 使用第一个测试用例的结果验证输出结构
    args = Args(test_params_1)
    result = await main(args)
    
    print("完整输出结构中的顶层字段:")
    for i, key in enumerate(result.keys(), 1):
        if key == 'strategy_name':
            print(f"  {i}. {key} ⭐ (新增的顶层策略名称)")
        else:
            print(f"  {i}. {key}")
    
    print(f"\n【JSON序列化测试】")
    try:
        json_str = json.dumps(result, indent=2, ensure_ascii=False)
        print("✅ 包含顶层strategy_name的结果可以正常JSON序列化")
        
        # 验证JSON中包含strategy_name
        if '"strategy_name"' in json_str:
            print("✅ JSON字符串中包含顶层strategy_name字段")
        else:
            print("❌ JSON字符串中未找到顶层strategy_name字段")
            
    except Exception as e:
        print(f"❌ JSON序列化失败: {e}")
    
    print(f"\n【错误处理测试】")
    # 测试错误情况下的strategy_name输出
    error_params = {
        'final_P25': 'invalid',  # 无效参数触发错误
        'final_P50': '8.0',
        'final_P75': '9.0',
        'user_type': '乐观开朗型',
        'cem_value': '1.2',
        'ei_value': '1.5',
        'eii_value': '0.4',
        'rsi_value': '0.8',
        'final_conf_score': '0.8',
        'crisis_probability': '0.2'
    }
    
    try:
        error_args = Args(error_params)
        error_result = await main(error_args)
        
        error_strategy_name = error_result.get('strategy_name')
        print(f"错误情况下的顶层策略名称: {error_strategy_name}")
        
        if error_strategy_name:
            print("✅ 错误处理情况下也正确输出了顶层strategy_name")
        else:
            print("❌ 错误处理情况下未输出顶层strategy_name")
            
    except Exception as e:
        print(f"错误处理测试异常: {e}")
    
    print(f"\n【功能总结】")
    print("🎉 策略名称顶层输出功能实现完成！")
    print("✅ 在输出结果的顶层添加了独立的 'strategy_name' 字段")
    print("✅ 保持了原有 'strategy_content_generation' 中的 'strategy_name' 字段")
    print("✅ 两个位置的策略名称保持一致")
    print("✅ 支持JSON序列化")
    print("✅ 错误处理情况下也能正常工作")
    print("✅ 方便直接访问策略名称，无需深入嵌套结构")

if __name__ == "__main__":
    asyncio.run(test_strategy_name_output())

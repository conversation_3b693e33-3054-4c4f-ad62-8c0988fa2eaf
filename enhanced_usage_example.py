"""
1.3 近期情绪画像建立 - 增强版完整使用示例
展示如何使用所有新增的输入参数
"""

import asyncio
import json
from datetime import datetime, timedelta
from recent_emotion_profile_builder import main, Args

async def enhanced_usage_example():
    """增强版完整使用示例"""
    
    print("=" * 80)
    print("1.3 近期情绪画像建立 - 增强版完整使用示例")
    print("=" * 80)
    
    # 构造丰富的测试数据
    base_time = datetime.now() - timedelta(days=7)
    working_memory = []
    
    # 生成25条记录，模拟真实用户交互数据
    for i in range(25):
        record_time = base_time + timedelta(hours=i*6, minutes=i*15)
        
        # 模拟乐观开朗型用户的渐进变化
        if i < 10:  # 前期：典型乐观开朗特征
            emo_value = str(7.0 + (i % 3) * 0.4)
            number = str(20 + (i % 5) * 3)
        elif i < 20:  # 中期：稍有波动
            emo_value = str(6.5 + (i % 4) * 0.3)
            number = str(18 + (i % 6) * 2)
        else:  # 后期：回归稳定
            emo_value = str(7.2 + (i % 2) * 0.3)
            number = str(22 + (i % 4) * 2)
        
        working_memory.append({
            'emo_value': emo_value,
            'number': number,
            'bstudio_create_time': record_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 完整的增强版参数配置
    enhanced_params = {
        # 原有必需参数
        'working_memory': working_memory,
        
        # 新增核心参数
        'user_type': '乐观开朗型',           # 用户类型（提升类型一致性评估）
        'P25': '6.8',                      # 长期基线P25（提升基线一致性评估）
        'P50': '7.5',                      # 长期基线P50
        'P75': '8.2',                      # 长期基线P75
        'weight_anomaly': 'normal',        # 全局权重异常标记
        
        # 原有可选参数（更精细配置）
        'quality_weights': (
            ['A'] * 8 +      # 前8条：高质量数据
            ['B'] * 12 +     # 中12条：标准质量数据
            ['C'] * 4 +      # 4条：低质量数据
            ['A'] * 1        # 最后1条：高质量数据
        ),
        'anomaly_flags': (
            ['normal'] * 20 +    # 大部分正常
            ['light'] * 4 +      # 少数轻微异常
            ['normal'] * 1       # 最后正常
        ),
        
        # 长期画像数据（用于对比）
        'long_term_profile': {
            'dominant_emotion_type': '乐观开朗型',
            'type_confidence': 0.85,
            'S_P25': 6.8,
            'S_P50': 7.5,
            'S_P75': 8.2
        }
    }
    
    print("\n【完整参数配置】")
    print(f"工作记忆数据量: {len(working_memory)} 条")
    print(f"用户类型: {enhanced_params['user_type']}")
    print(f"长期基线: P25={enhanced_params['P25']}, P50={enhanced_params['P50']}, P75={enhanced_params['P75']}")
    print(f"权重异常级别: {enhanced_params['weight_anomaly']}")
    print(f"质量权重分布: A级{enhanced_params['quality_weights'].count('A')}条, B级{enhanced_params['quality_weights'].count('B')}条, C级{enhanced_params['quality_weights'].count('C')}条")
    print(f"异常标记分布: 正常{enhanced_params['anomaly_flags'].count('normal')}条, 轻微异常{enhanced_params['anomaly_flags'].count('light')}条")
    
    # 执行增强版分析
    print("\n【执行增强版近期情绪画像分析】")
    args = Args(enhanced_params)
    result = await main(args)
    
    if result.get('error'):
        print(f"❌ 分析失败: {result.get('error_message')}")
        return
    
    print("✅ 分析成功完成")
    
    # 核心分析结果
    print(f"\n【核心分析结果】")
    print(f"主导情绪类型: {result.get('dominant_emotion_type')}")
    print(f"类型置信度: {result.get('type_confidence'):.3f}")
    print(f"分析置信度: {result.get('analysis_confidence'):.3f}")
    print(f"数据数量: {result.get('data_count')}")
    
    # 观测基线与长期基线对比
    print(f"\n【基线对比分析】")
    observed = result.get('observed_baseline', {})
    long_term = enhanced_params
    print("观测基线 vs 长期基线:")
    print(f"  P25: {observed.get('P25'):.2f} vs {float(long_term['P25']):.2f} (差异: {abs(observed.get('P25', 0) - float(long_term['P25'])):.2f})")
    print(f"  P50: {observed.get('P50'):.2f} vs {float(long_term['P50']):.2f} (差异: {abs(observed.get('P50', 0) - float(long_term['P50'])):.2f})")
    print(f"  P75: {observed.get('P75'):.2f} vs {float(long_term['P75']):.2f} (差异: {abs(observed.get('P75', 0) - float(long_term['P75'])):.2f})")
    
    # 增强版置信度分解
    print(f"\n【增强版置信度分解】")
    confidence_breakdown = result.get('detailed_analysis', {}).get('confidence_breakdown', {})
    print(f"数据充分性: {confidence_breakdown.get('data_sufficiency', 0):.3f}")
    print(f"内部一致性: {confidence_breakdown.get('internal_consistency', 0):.3f}")
    print(f"时间稳定性: {confidence_breakdown.get('time_stability', 0):.3f}")
    print(f"类型一致性: {confidence_breakdown.get('type_consistency', 0):.3f} 🆕")
    print(f"基线一致性: {confidence_breakdown.get('baseline_consistency', 0):.3f} 🆕")
    print(f"综合置信度: {confidence_breakdown.get('comprehensive_confidence', 0):.3f}")
    
    # 情绪类型得分详情
    print(f"\n【情绪类型得分分布】")
    emotion_scores = result.get('emotion_type_scores', {})
    sorted_scores = sorted(emotion_scores.items(), key=lambda x: x[1], reverse=True)
    for i, (emotion_type, score) in enumerate(sorted_scores, 1):
        marker = "👑" if i == 1 else f"{i}."
        print(f"{marker} {emotion_type}: {score:.3f}")
    
    # 增强参数使用情况
    print(f"\n【增强参数使用情况】")
    enhanced_info = result.get('processing_info', {}).get('enhanced_parameters', {})
    print(f"✅ 用户类型已提供: {enhanced_info.get('user_type_provided')} ({enhanced_info.get('user_type_value')})")
    print(f"✅ 长期基线已提供: {enhanced_info.get('long_term_baseline_provided')}")
    print(f"✅ 权重异常已应用: {enhanced_info.get('weight_anomaly_applied')} ({enhanced_info.get('weight_anomaly_level')})")
    
    # 数据处理统计
    print(f"\n【数据处理统计】")
    processing_info = result.get('processing_info', {})
    print(f"原始数据数量: {processing_info.get('raw_data_count')}")
    print(f"处理后数据数量: {processing_info.get('processed_data_count')}")
    print(f"总有效权重: {processing_info.get('total_effective_weight'):.3f}")
    
    # 质量控制指标
    print(f"\n【质量控制指标】")
    quality_metrics = result.get('quality_metrics', {})
    print(f"数据充分性等级: {quality_metrics.get('data_sufficiency_level')}")
    print(f"分析可靠性等级: {quality_metrics.get('analysis_reliability')}")
    print(f"输入验证警告: {len(quality_metrics.get('input_validation', {}).get('warnings', []))} 个")
    
    # 给1.4节的标准化输出
    print(f"\n【给1.4节贝叶斯更新的标准化输出】")
    standardized_output = {
        "dominant_emotion_type": result.get('dominant_emotion_type'),
        "type_confidence": result.get('type_confidence'),
        "emotion_type_scores": result.get('emotion_type_scores'),
        "observed_baseline": result.get('observed_baseline'),
        "data_count": result.get('data_count'),
        "analysis_confidence": result.get('analysis_confidence'),
        # 增强版额外信息
        "enhanced_confidence_metrics": {
            "type_consistency": confidence_breakdown.get('type_consistency'),
            "baseline_consistency": confidence_breakdown.get('baseline_consistency')
        }
    }
    print(json.dumps(standardized_output, indent=2, ensure_ascii=False))
    
    print(f"\n【增强版功能总结】")
    print("🎯 新增参数显著提升了分析的精度和可靠性:")
    print("  ✅ user_type: 提供类型一致性评估基准")
    print("  ✅ P25/P50/P75: 增加基线偏差分析维度")
    print("  ✅ weight_anomaly: 支持全局数据质量调整")
    print("  ✅ 多维度置信度: 更全面的可靠性评估")
    print("  ✅ 增强验证: 减少输入参数警告")
    print("  ✅ 处理追踪: 完整的参数使用记录")
    
    print(f"\n🚀 建议在实际使用中提供所有新增参数以获得最佳分析效果！")

if __name__ == "__main__":
    asyncio.run(enhanced_usage_example())

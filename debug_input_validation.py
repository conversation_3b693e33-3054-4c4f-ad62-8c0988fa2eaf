"""
调试输入参数验证问题
"""

import asyncio
import json
from recent_emotion_profile_builder import main, Args, validate_input_parameters

async def debug_input_validation():
    """调试输入参数验证"""
    
    print("=" * 70)
    print("调试输入参数验证问题")
    print("=" * 70)
    
    # 测试1：空参数
    print("\n【测试1：空参数】")
    empty_params = {}
    validation1 = validate_input_parameters(empty_params)
    print(f"验证结果: {validation1}")
    
    # 测试2：空的working_memory
    print("\n【测试2：空的working_memory】")
    empty_working_memory_params = {
        'working_memory': []
    }
    validation2 = validate_input_parameters(empty_working_memory_params)
    print(f"验证结果: {validation2}")
    
    # 测试3：正确的working_memory格式
    print("\n【测试3：正确的working_memory格式】")
    correct_params = {
        'working_memory': [
            {
                'emo_value': '7.0',
                'number': '20',
                'bstudio_create_time': '2025-07-15 10:00:00'
            },
            {
                'emo_value': '6.5',
                'number': '25',
                'bstudio_create_time': '2025-07-15 11:00:00'
            }
        ]
    }
    validation3 = validate_input_parameters(correct_params)
    print(f"验证结果: {validation3}")
    
    # 测试4：使用正确格式调用main函数
    print("\n【测试4：使用正确格式调用main函数】")
    try:
        args = Args(correct_params)
        result = await main(args)
        
        if result.get('error'):
            print(f"❌ 调用失败: {result.get('error_message')}")
            print(f"验证结果: {result.get('validation_result')}")
        else:
            print("✅ 调用成功！")
            print(f"主导情绪类型: {result.get('dominant_emotion_type')}")
            print(f"P25: {result.get('observed_baseline', {}).get('P25')}")
            print(f"P50: {result.get('observed_baseline', {}).get('P50')}")
            print(f"P75: {result.get('observed_baseline', {}).get('P75')}")
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    # 测试5：使用您的真实数据格式
    print("\n【测试5：使用真实数据格式】")
    real_data_params = {
        'working_memory': [
            {
                "P25": "5.89",
                "P50": "6.83", 
                "P75": "8.66",
                "bstudio_create_time": "2025-07-14 22:42:00 +0800 CST",
                "conversation": "你说的这个积极重构法与冲突解决技巧是什么东西呀",
                "delta_days": 1,
                "emo_value": "6",
                "emotion_change": 1,
                "emotion_deviation": "1.2199999999999998",
                "feature_score": "0.9396607142857142",
                "number": "23",
                "priority_score": "6.782180357142858",
                "quality_score": "9.6",
                "rsi_history": "0.443",
                "weight_anomaly": ""
            },
            {
                "P25": "5.89",
                "P50": "6.83",
                "P75": "8.67", 
                "bstudio_create_time": "2025-07-14 22:43:09 +0800 CST",
                "conversation": "哦，听起来不错，那么就请你重点讲一下冲突解决技巧吧",
                "delta_days": 1,
                "emo_value": "5",
                "emotion_change": 2,
                "emotion_deviation": "2.2199999999999998",
                "feature_score": "1.4236607142857145",
                "number": "23",
                "priority_score": "6.580580357142858",
                "quality_score": "9.6",
                "rsi_history": "0.438",
                "weight_anomaly": ""
            }
        ]
    }
    
    validation5 = validate_input_parameters(real_data_params)
    print(f"验证结果: {validation5}")
    
    if validation5['is_valid']:
        try:
            args = Args(real_data_params)
            result = await main(args)
            
            if result.get('error'):
                print(f"❌ 真实数据调用失败: {result.get('error_message')}")
            else:
                print("✅ 真实数据调用成功！")
                print(f"主导情绪类型: {result.get('dominant_emotion_type')}")
                baseline = result.get('observed_baseline', {})
                print(f"P25: {baseline.get('P25')}, P50: {baseline.get('P50')}, P75: {baseline.get('P75')}")
        except Exception as e:
            print(f"❌ 真实数据调用异常: {e}")
    
    print("\n【调试建议】")
    print("1. 确保working_memory参数存在且不为空")
    print("2. 确保working_memory是数组格式")
    print("3. 确保每条记录包含必需字段: emo_value, number, bstudio_create_time")
    print("4. 检查时间格式是否正确")
    
    print("\n【正确的调用示例】")
    example_code = '''
params = {
    'working_memory': [
        {
            'emo_value': '7.0',
            'number': '20', 
            'bstudio_create_time': '2025-07-15 10:00:00'
        }
        # 更多数据...
    ]
}

args = Args(params)
result = await main(args)
'''
    print(example_code)

if __name__ == "__main__":
    asyncio.run(debug_input_validation())

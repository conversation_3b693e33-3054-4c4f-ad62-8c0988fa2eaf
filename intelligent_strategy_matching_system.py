"""
智能策略匹配系统
基于方案分析报告2025.6.23-55_修改版的三层决策树架构
将计算1-8的量化分析结果转化为精准的用户情绪识别和个性化聊天内容策略
适配Python 3.11.3标准库环境
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# 输入输出类型定义
class Args:
    def __init__(self, params: Dict[str, Any]):
        self.params = params

class Output:
    def __init__(self, data: Dict[str, Any]):
        self.data = data

    def __getitem__(self, key):
        return self.data[key]

    def __setitem__(self, key, value):
        self.data[key] = value

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，支持JSON序列化"""
        return self.data

    def __iter__(self):
        """支持迭代"""
        return iter(self.data)

    def keys(self):
        """支持keys()方法"""
        return self.data.keys()

    def values(self):
        """支持values()方法"""
        return self.data.values()

    def items(self):
        """支持items()方法"""
        return self.data.items()

    def get(self, key, default=None):
        """支持get()方法"""
        return self.data.get(key, default)

    def __repr__(self):
        """字符串表示"""
        return f"Output({self.data})"

    def __str__(self):
        """字符串表示"""
        return str(self.data)

class IntelligentStrategyMatcher:
    """智能策略匹配核心引擎 - 基于三层决策树架构"""

    def __init__(self):
        # 用户类型特征矩阵（基于方案分析报告）
        self.user_type_features = {
            '乐观开朗型': {
                'cem_range': (0.2, 2.0),
                'ei_range': (0.8, 2.0),
                'rsi_range': (0.6, 1.0),
                'crisis_threshold': 0.3,
                'special_condition': 'positive_momentum'
            },
            '悲观消极型': {
                'cem_range': (-2.0, 0.0),
                'ei_range': (0.5, 1.5),
                'rsi_range': (0.3, 0.7),
                'crisis_threshold': 0.6,
                'special_condition': 'negative_momentum'
            },
            '沉稳内敛型': {
                'cem_range': (-0.3, 0.3),
                'ei_range': (0.2, 0.8),
                'rsi_range': (0.7, 1.0),
                'crisis_threshold': 0.4,
                'special_condition': 'high_inertia'  # EII > 0.7
            },
            '情绪敏感型': {
                'cem_range': None,  # 波动性高，不限范围
                'ei_range': (1.2, 2.0),
                'rsi_range': None,  # 不限
                'crisis_threshold': 0.4,
                'special_condition': 'high_intensity'
            },
            '适应调整型': {
                'cem_range': None,  # 不限
                'ei_range': None,   # 不限
                'rsi_range': None,  # 不限
                'crisis_threshold': None,  # 不限
                'special_condition': 'low_confidence'  # Confidence < 0.6
            }
        }

        # 特征权重分配（基于方案分析报告）
        self.feature_weights = {
            'cem': 0.25,
            'ei': 0.20,
            'rsi': 0.20,
            'crisis': 0.25,
            'confidence': 0.10
        }

        # 情绪状态识别规则（基于方案分析报告的七种核心情绪状态）
        self.emotion_states = {
            '危机焦虑': {'priority': 1, 'condition': 'crisis_prob > 0.7'},
            '兴奋积极': {'priority': 2, 'condition': 'cem > 0.5 and ei > 1.0'},
            '沮丧消极': {'priority': 3, 'condition': 'cem < -0.5 and ei > 1.0'},
            '平静稳定': {'priority': 4, 'condition': 'abs(cem) < 0.3 and ei < 0.5'},
            '希望乐观': {'priority': 5, 'condition': 'cem > 0.2 and development > 0.6'},
            '担忧关切': {'priority': 6, 'condition': 'cem < -0.2 and crisis_prob > 0.4'},
            '中性平衡': {'priority': 7, 'condition': 'default'}
        }

        # 策略内容模板（基于方案分析报告的用户类型×情绪状态策略矩阵）
        self.strategy_templates = self._initialize_strategy_templates()

    def _initialize_strategy_templates(self) -> Dict[str, Dict[str, Dict[str, str]]]:
        """初始化策略内容模板"""
        return {
            '乐观开朗型': {
                '兴奋积极': {
                    'strategy': '共鸣放大策略',
                    'style': '热情洋溢、积极回应',
                    'content': '哇！听起来真的很棒呢！你的兴奋感染了我，快跟我分享更多细节吧！'
                },
                '希望乐观': {
                    'strategy': '鼓励强化策略',
                    'style': '支持肯定、展望未来',
                    'content': '你的乐观态度真让人佩服！我相信以你的积极心态，一定能实现目标的！'
                },
                '平静稳定': {
                    'strategy': '轻松互动策略',
                    'style': '轻松愉快、适度活跃',
                    'content': '看起来你今天心情不错呢～有什么有趣的事情想聊聊吗？'
                },
                '担忧关切': {
                    'strategy': '温和转向策略',
                    'style': '理解安抚、积极引导',
                    'content': '我理解你的担心，不过以你平时的乐观，相信很快就能找到解决办法的！'
                },
                '沮丧消极': {
                    'strategy': '支持重构策略',
                    'style': '温暖理解、希望重建',
                    'content': '虽然现在有些困难，但我知道你内心的阳光还在。让我们一起想想积极的一面吧！'
                }
            },
            '悲观消极型': {
                '危机焦虑': {
                    'strategy': '深度安抚策略',
                    'style': '极度温和、专业支持',
                    'content': '我能感受到你现在很不容易，这种感觉确实很难受。你不是一个人，我会一直陪着你的。'
                },
                '沮丧消极': {
                    'strategy': '完全接纳策略',
                    'style': '深度共情、无条件理解',
                    'content': '你的感受完全可以理解，任何人遇到这种情况都会难过的。慢慢来，不用急着好起来。'
                },
                '担忧关切': {
                    'strategy': '细致关怀策略',
                    'style': '小心呵护、耐心倾听',
                    'content': '我注意到你有些担心，能跟我说说具体是什么让你不安吗？我会认真听的。'
                },
                '平静稳定': {
                    'strategy': '温和陪伴策略',
                    'style': '安静存在、不施压力',
                    'content': '今天看起来你还挺平静的，这很好。我就在这里陪着你，想聊什么都可以。'
                },
                '希望乐观': {
                    'strategy': '谨慎鼓励策略',
                    'style': '温和肯定、避免过度',
                    'content': '看到你有这样的想法我很欣慰，虽然路可能不容易，但你已经迈出了重要的一步。'
                }
            },
            '沉稳内敛型': {
                '平静稳定': {
                    'strategy': '深度对话策略',
                    'style': '理性平和、有深度',
                    'content': '你总是这么沉稳，让人很有安全感。最近有什么值得深入思考的事情吗？'
                },
                '中性平衡': {
                    'strategy': '智慧引导策略',
                    'style': '启发思考、尊重空间',
                    'content': '从你的表达中能感受到你的思考深度。这个问题确实值得仔细考虑。'
                },
                '担忧关切': {
                    'strategy': '理性分析策略',
                    'style': '逻辑清晰、条理分明',
                    'content': '我理解你的考虑，让我们一起理性分析一下这个问题的各个方面。'
                },
                '希望乐观': {
                    'strategy': '稳健支持策略',
                    'style': '踏实肯定、长远视角',
                    'content': '你的计划很周全，这种稳扎稳打的方式往往能取得更持久的成果。'
                },
                '沮丧消极': {
                    'strategy': '空间尊重策略',
                    'style': '不过度干预、给予空间',
                    'content': '我能感受到你现在需要一些时间思考。我会在这里，你准备好了随时可以聊。'
                }
            },
            '情绪敏感型': {
                '危机焦虑': {
                    'strategy': '极致呵护策略',
                    'style': '极度温柔、情感包裹',
                    'content': '宝贝，我能感受到你现在很痛苦，你的每一份感受我都理解。让我紧紧抱抱你好吗？'
                },
                '兴奋积极': {
                    'strategy': '温和共鸣策略',
                    'style': '适度回应、避免过激',
                    'content': '看到你这么开心我也很高兴呢～不过记得保护好自己的情绪，慢慢享受这份快乐。'
                },
                '沮丧消极': {
                    'strategy': '深度陪伴策略',
                    'style': '情感同步、心灵连接',
                    'content': '我的心和你的心连在一起，你的痛苦我都能感受到。让我陪你一起度过这段艰难时光。'
                },
                '担忧关切': {
                    'strategy': '细腻安抚策略',
                    'style': '敏感察觉、精准回应',
                    'content': '我注意到你语气中的一丝不安，是什么让你担心了？每一个细微的感受都很重要。'
                },
                '平静稳定': {
                    'strategy': '温柔维护策略',
                    'style': '小心维护、避免打扰',
                    'content': '你现在的平静很珍贵，我会小心守护这份安宁。有需要的时候轻轻告诉我就好。'
                }
            },
            '适应调整型': {
                '中性平衡': {
                    'strategy': '探索引导策略',
                    'style': '开放好奇、鼓励探索',
                    'content': '你现在处在一个很有意思的阶段，有很多可能性等着你去发现。想探索哪个方向呢？'
                },
                '担忧关切': {
                    'strategy': '变化支持策略',
                    'style': '理解变化、提供支撑',
                    'content': '面对变化时有些不安是很正常的，这说明你在成长。我会支持你度过这个转换期。'
                },
                '希望乐观': {
                    'strategy': '成长激励策略',
                    'style': '肯定进步、展望成长',
                    'content': '你的适应能力真的很强！每一次调整都让你变得更加成熟和智慧。'
                },
                '沮丧消极': {
                    'strategy': '转换安抚策略',
                    'style': '理解困难、引导转换',
                    'content': '转换期确实不容易，但这也意味着你正在突破自己。困难是暂时的，成长是永恒的。'
                },
                '平静稳定': {
                    'strategy': '稳定维护策略',
                    'style': '珍惜稳定、适度刺激',
                    'content': '难得你在变化中找到了平衡，这很不容易。要不要尝试一些新的小挑战？'
                }
            }
        }

    def safe_float_convert(self, value: str, default: float = 0.0) -> float:
        """安全转换字符串为浮点数"""
        try:
            if value is None or value == '':
                return default
            return float(value)
        except (ValueError, TypeError):
            return default

    def identify_user_type(self, params: Dict[str, str]) -> tuple[str, float]:
        """第一层：用户类型识别决策"""
        # 提取关键特征
        cem_value = self.safe_float_convert(params.get('cem_value', '0.0'))
        ei_value = self.safe_float_convert(params.get('ei_value', '0.0'))
        rsi_value = self.safe_float_convert(params.get('rsi_value', '0.5'))
        crisis_prob = self.safe_float_convert(params.get('crisis_probability', '0.5'))
        confidence = self.safe_float_convert(params.get('final_conf_score', '0.5'))
        eii_value = self.safe_float_convert(params.get('eii_value', '0.5'))

        # 补充参数：增强用户类型识别精度
        cem_grade = params.get('cem_grade', 'C')
        ei_confidence = self.safe_float_convert(params.get('ei_confidence', '0.5'))
        health_score = self.safe_float_convert(params.get('health_score', '0.5'))
        trend_prediction = params.get('trend_prediction', 'stable')

        # 计算每种用户类型的匹配度
        type_scores = {}

        for user_type, features in self.user_type_features.items():
            score = 0.0

            # CEM动量匹配
            if features['cem_range']:
                min_cem, max_cem = features['cem_range']
                if min_cem <= cem_value <= max_cem:
                    score += self.feature_weights['cem']
            elif features['cem_range'] is None:  # 不限范围
                score += self.feature_weights['cem'] * 0.5  # 给予中等分数

            # EI强度匹配
            if features['ei_range']:
                min_ei, max_ei = features['ei_range']
                if min_ei <= ei_value <= max_ei:
                    score += self.feature_weights['ei']
            elif features['ei_range'] is None:
                score += self.feature_weights['ei'] * 0.5

            # RSI稳定性匹配
            if features['rsi_range']:
                min_rsi, max_rsi = features['rsi_range']
                if min_rsi <= rsi_value <= max_rsi:
                    score += self.feature_weights['rsi']
            elif features['rsi_range'] is None:
                score += self.feature_weights['rsi'] * 0.5

            # 危机阈值匹配
            if features['crisis_threshold'] is not None:
                if user_type == '悲观消极型':
                    # 悲观消极型：危机概率应该高于阈值
                    if crisis_prob > features['crisis_threshold']:
                        score += self.feature_weights['crisis']
                else:
                    # 其他类型：危机概率应该低于阈值
                    if crisis_prob < features['crisis_threshold']:
                        score += self.feature_weights['crisis']
            else:
                score += self.feature_weights['crisis'] * 0.5

            # 特殊条件匹配（增强版，利用补充参数）
            special_condition = features['special_condition']
            special_score = 0.0

            if special_condition == 'high_inertia' and eii_value > 0.7:
                special_score += 0.6
                # CEM等级稳定性加成
                if cem_grade in ['A', 'B']:
                    special_score += 0.4
            elif special_condition == 'low_confidence' and confidence < 0.6:
                special_score += 0.5
                # 趋势预测不确定性加成
                if trend_prediction == 'stable':
                    special_score += 0.3
                # EI置信度低加成
                if ei_confidence < 0.6:
                    special_score += 0.2
            elif special_condition == 'high_intensity' and ei_value >= 1.2:
                special_score += 0.6
                # EI置信度高加成
                if ei_confidence > 0.8:
                    special_score += 0.4
            elif special_condition == 'positive_momentum':
                if cem_value > 0 and cem_grade in ['A', 'B']:
                    special_score += 0.7
                if trend_prediction == 'positive':
                    special_score += 0.3
            elif special_condition == 'negative_momentum':
                if cem_value < 0 and cem_grade in ['C', 'D']:
                    special_score += 0.7
                if trend_prediction == 'negative':
                    special_score += 0.3

            score += self.feature_weights['confidence'] * min(1.0, special_score)

            type_scores[user_type] = score

        # 选择得分最高的用户类型
        best_type = max(type_scores, key=type_scores.get)
        best_score = type_scores[best_type]

        # 计算置信度
        max_possible_score = sum(self.feature_weights.values())
        confidence_score = best_score / max_possible_score

        return best_type, confidence_score

    def evaluate_emotional_state(self, params: Dict[str, str]) -> tuple[str, float]:
        """第二层：情绪状态评估（增强版，利用补充参数）"""
        # 提取关键参数
        cem_value = self.safe_float_convert(params.get('cem_value', '0.0'))
        ei_value = self.safe_float_convert(params.get('ei_value', '0.0'))
        crisis_prob = self.safe_float_convert(params.get('crisis_probability', '0.5'))
        development = self.safe_float_convert(params.get('health_score', '0.5'))  # 使用health_score作为发展潜力

        # 补充参数：增强情绪状态评估精度
        cem_grade = params.get('cem_grade', 'C')
        ei_confidence = self.safe_float_convert(params.get('ei_confidence', '0.5'))
        trend_prediction = params.get('trend_prediction', 'stable')

        # CEM等级到置信度映射
        cem_grade_confidence = {'A': 0.9, 'B': 0.7, 'C': 0.5, 'D': 0.3}.get(cem_grade, 0.5)

        # 趋势预测到发展潜力调整
        trend_adjustment = {'positive': 0.2, 'stable': 0.0, 'negative': -0.2}.get(trend_prediction, 0.0)
        adjusted_development = max(0.0, min(1.0, development + trend_adjustment))

        # 按优先级顺序评估情绪状态（增强版）
        # 1. 危机焦虑 (最高优先级)
        if crisis_prob > 0.7:
            base_confidence = 1 - abs(0.7 - crisis_prob) / 0.3
            # CEM等级和趋势预测调整
            grade_adjustment = (0.9 - cem_grade_confidence) * 0.2
            trend_adjustment_factor = {'negative': 0.1, 'stable': 0.0, 'positive': -0.1}.get(trend_prediction, 0.0)
            confidence = min(1.0, base_confidence + grade_adjustment + trend_adjustment_factor)
            return '危机焦虑', confidence

        # 2. 兴奋积极
        if cem_value > 0.5 and ei_value > 1.0:
            base_confidence = min(1.0, (cem_value - 0.5) / 1.5 * 0.5 + (ei_value - 1.0) / 1.0 * 0.5)
            # EI置信度和CEM等级调整
            ei_adjustment = (ei_confidence - 0.5) * 0.3
            grade_adjustment = (cem_grade_confidence - 0.5) * 0.2
            confidence = min(1.0, base_confidence + ei_adjustment + grade_adjustment)
            return '兴奋积极', confidence

        # 3. 沮丧消极
        if cem_value < -0.5 and ei_value > 1.0:
            base_confidence = min(1.0, abs(cem_value + 0.5) / 1.5 * 0.5 + (ei_value - 1.0) / 1.0 * 0.5)
            # EI置信度和健康分数调整
            ei_adjustment = (ei_confidence - 0.5) * 0.2
            health_adjustment = (0.5 - development) * 0.3
            confidence = min(1.0, base_confidence + ei_adjustment + health_adjustment)
            return '沮丧消极', confidence

        # 4. 平静稳定
        if abs(cem_value) < 0.3 and ei_value < 0.5:
            base_confidence = min(1.0, (0.3 - abs(cem_value)) / 0.3 * 0.5 + (0.5 - ei_value) / 0.5 * 0.5)
            # CEM等级稳定性调整
            stability_bonus = 0.2 if cem_grade in ['A', 'B'] else 0.0
            confidence = min(1.0, base_confidence + stability_bonus)
            return '平静稳定', confidence

        # 5. 希望乐观
        if cem_value > 0.2 and adjusted_development > 0.6:
            base_confidence = min(1.0, (cem_value - 0.2) / 1.8 * 0.5 + (adjusted_development - 0.6) / 0.4 * 0.5)
            # 趋势预测正向调整
            trend_bonus = 0.2 if trend_prediction == 'positive' else 0.0
            confidence = min(1.0, base_confidence + trend_bonus)
            return '希望乐观', confidence

        # 6. 担忧关切
        if cem_value < -0.2 and crisis_prob > 0.4:
            base_confidence = min(1.0, abs(cem_value + 0.2) / 1.8 * 0.5 + (crisis_prob - 0.4) / 0.6 * 0.5)
            # CEM等级不稳定性调整
            instability_bonus = 0.2 if cem_grade in ['C', 'D'] else 0.0
            confidence = min(1.0, base_confidence + instability_bonus)
            return '担忧关切', confidence

        # 7. 中性平衡 (默认状态)
        # 基于补充参数调整中性平衡的置信度
        base_confidence = 0.5
        if ei_confidence < 0.5:
            base_confidence -= 0.1  # EI置信度低，降低中性平衡置信度
        if cem_grade == 'C':
            base_confidence += 0.1  # CEM等级中等，适合中性平衡
        return '中性平衡', max(0.3, base_confidence)

    def generate_strategy_content(self, user_type: str, emotional_state: str, params: Dict[str, str]) -> Dict[str, Any]:
        """第三层：策略内容生成（增强版，利用补充参数）"""
        # 获取基础策略模板
        base_template = self.strategy_templates.get(user_type, {}).get(emotional_state)

        if not base_template:
            # 如果没有找到对应的模板，使用默认策略
            base_template = {
                'strategy': '通用支持策略',
                'style': '温和理解、适度回应',
                'content': '我理解你现在的感受，让我们一起面对这个情况。'
            }

        # 提取参数进行动态内容调整
        ei_value = self.safe_float_convert(params.get('ei_value', '0.0'))
        rsi_value = self.safe_float_convert(params.get('rsi_value', '0.5'))

        # 补充参数：增强个性化精度
        cem_grade = params.get('cem_grade', 'C')
        ei_confidence = self.safe_float_convert(params.get('ei_confidence', '0.5'))
        health_score = self.safe_float_convert(params.get('health_score', '0.5'))
        trend_prediction = params.get('trend_prediction', 'stable')

        # 增强版强度修饰符（考虑EI置信度）
        base_intensity = ei_value
        confidence_adjustment = (ei_confidence - 0.5) * 0.5  # 置信度调整
        adjusted_intensity = base_intensity + confidence_adjustment

        if adjusted_intensity > 1.5:
            intensity_modifier = "非常"
        elif adjusted_intensity > 1.0:
            intensity_modifier = "很"
        else:
            intensity_modifier = "有些"

        # 增强版关系修饰符（考虑CEM等级稳定性）
        base_relationship = rsi_value
        grade_stability = {'A': 0.2, 'B': 0.1, 'C': 0.0, 'D': -0.1}.get(cem_grade, 0.0)
        adjusted_relationship = base_relationship + grade_stability

        if adjusted_relationship > 0.8:
            relationship_modifier = "我们的关系很稳定"
        elif adjusted_relationship > 0.6:
            relationship_modifier = "我们正在建立信任"
        else:
            relationship_modifier = "让我们慢慢了解"

        # 趋势预测影响的时间语境
        time_context = {
            'positive': '未来充满希望',
            'stable': '当下值得珍惜',
            'negative': '困难终会过去'
        }.get(trend_prediction, '当下值得珍惜')

        # 健康分数影响的支持强度
        if health_score > 0.7:
            support_level = '轻度支持'
        elif health_score > 0.4:
            support_level = '中度支持'
        else:
            support_level = '深度支持'

        # 生成个性化内容
        personalized_content = base_template['content']

        # 根据情绪强度调整内容
        if '很' in personalized_content and intensity_modifier != "很":
            personalized_content = personalized_content.replace('很', intensity_modifier)

        return {
            'strategy_name': base_template['strategy'],
            'communication_style': base_template['style'],
            'content': personalized_content,
            'intensity_modifier': intensity_modifier,
            'relationship_context': relationship_modifier,
            'time_context': time_context,
            'support_level': support_level,
            'user_type': user_type,
            'emotional_state': emotional_state,
            'personalization_factors': {
                'cem_grade_stability': cem_grade,
                'ei_confidence_level': ei_confidence,
                'health_support_need': health_score,
                'trend_outlook': trend_prediction
            }
        }

    def calculate_overall_confidence(self, type_confidence: float, state_confidence: float, content_confidence: float = 0.8) -> float:
        """计算综合置信度"""
        # 基于方案分析报告的置信度计算公式
        alpha, beta, gamma = 0.4, 0.4, 0.2
        overall_confidence = alpha * type_confidence + beta * state_confidence + gamma * content_confidence
        return min(1.0, max(0.0, overall_confidence))

def _validate_input_parameters(params: Dict[str, str]) -> Dict[str, Any]:
    """验证输入参数的有效性"""
    validation_result = {
        'is_valid': True,
        'missing_params': [],
        'invalid_ranges': [],
        'warnings': []
    }

    # 必需参数检查
    required_params = [
        'final_P25', 'final_P50', 'final_P75', 'user_type', 'cem_value',
        'ei_value', 'eii_value', 'rsi_value', 'final_conf_score', 'crisis_probability'
    ]

    # 建议补充参数（不是必需的，但建议提供以提升精度）
    recommended_params = ['cem_grade', 'ei_confidence', 'health_score', 'trend_prediction']

    for param in required_params:
        if param not in params or params[param] == '':
            validation_result['missing_params'].append(param)
            validation_result['is_valid'] = False

    # 检查建议参数
    for param in recommended_params:
        if param not in params or params[param] == '':
            validation_result['warnings'].append(f'建议提供参数 {param} 以提升匹配精度')

    # 数值范围检查
    numeric_ranges = {
        'cem_value': (-2.0, 2.0),
        'ei_value': (0.0, 2.0),
        'eii_value': (0.0, 1.0),
        'rsi_value': (0.0, 1.0),
        'final_conf_score': (0.0, 1.0),
        'crisis_probability': (0.0, 1.0),
        'ei_confidence': (0.0, 1.0),
        'health_score': (0.0, 1.0)
    }

    for param, (min_val, max_val) in numeric_ranges.items():
        if param in params:
            try:
                value = float(params[param])
                if not (min_val <= value <= max_val):
                    validation_result['invalid_ranges'].append(f'{param}: {value} (应在 {min_val}-{max_val} 范围内)')
                    validation_result['is_valid'] = False
            except (ValueError, TypeError):
                validation_result['invalid_ranges'].append(f'{param}: 无法转换为数值')
                validation_result['is_valid'] = False

    # 用户类型检查
    valid_user_types = ['乐观开朗型', '悲观消极型', '沉稳内敛型', '情绪敏感型', '适应调整型']
    if 'user_type' in params and params['user_type'] not in valid_user_types:
        validation_result['warnings'].append(f'用户类型 "{params["user_type"]}" 不在预定义类型中，将使用适应调整型')

    # CEM等级检查
    valid_cem_grades = ['A', 'B', 'C', 'D', '稳定', '基本稳定', '波动', '不稳定']
    if 'cem_grade' in params and params['cem_grade'] not in valid_cem_grades:
        validation_result['warnings'].append(f'CEM等级 "{params["cem_grade"]}" 不在预定义等级中，将使用默认值C')

    # 趋势预测检查
    valid_trends = ['positive', 'stable', 'negative', '积极', '稳定', '消极']
    if 'trend_prediction' in params and params['trend_prediction'] not in valid_trends:
        validation_result['warnings'].append(f'趋势预测 "{params["trend_prediction"]}" 不在预定义类型中，将使用默认值stable')

    return validation_result

async def main(args: Args) -> Dict[str, Any]:
    """
    智能策略匹配系统主函数
    基于三层决策树架构：用户类型识别 → 情绪状态评估 → 策略内容生成
    """
    params = args.params

    try:
        # 输入参数验证
        validation = _validate_input_parameters(params)

        # 初始化策略匹配器
        matcher = IntelligentStrategyMatcher()

        # 第一层：用户类型识别
        user_type, type_confidence = matcher.identify_user_type(params)

        # 第二层：情绪状态评估
        emotional_state, state_confidence = matcher.evaluate_emotional_state(params)

        # 第三层：策略内容生成
        strategy_content = matcher.generate_strategy_content(user_type, emotional_state, params)

        # 计算综合置信度
        overall_confidence = matcher.calculate_overall_confidence(type_confidence, state_confidence)

        # 构建输出结果
        ret = Output({
            # 系统信息
            "system_id": "intelligent_strategy_matching_system",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "algorithm_version": "three_layer_decision_tree_v1.0",

            # 第一层结果：用户类型识别
            "user_type_identification": {
                "identified_type": user_type,
                "type_confidence": round(type_confidence, 3),
                "alternative_types": []  # 可以扩展为显示其他可能的类型
            },

            # 第二层结果：情绪状态评估
            "emotional_state_evaluation": {
                "current_state": emotional_state,
                "state_confidence": round(state_confidence, 3),
                "priority_level": matcher.emotion_states.get(emotional_state, {}).get('priority', 7)
            },

            # 第三层结果：策略内容生成
            "strategy_content_generation": strategy_content,

            # 策略名称（顶层单独输出）
            "strategy_name": strategy_content['strategy_name'],

            # 综合结果
            "matching_results": {
                "overall_confidence": round(overall_confidence, 3),
                "decision_path": f"{user_type} → {emotional_state} → {strategy_content['strategy_name']}",
                "content_personalization": {
                    "intensity_level": strategy_content['intensity_modifier'],
                    "relationship_context": strategy_content['relationship_context']
                }
            },

            # 质量控制
            "quality_metrics": {
                "input_validation": validation,
                "confidence_breakdown": {
                    "type_confidence": round(type_confidence, 3),
                    "state_confidence": round(state_confidence, 3),
                    "content_confidence": 0.8
                },
                "decision_reliability": "high" if overall_confidence > 0.7 else "medium" if overall_confidence > 0.5 else "low"
            },

            # 元数据
            "metadata": {
                "input_parameters_count": len(params),
                "processing_layers": 3,
                "decision_tree_depth": 3,
                "personalization_factors": ["user_type", "emotional_state", "intensity", "relationship_context"]
            }
        })

        return ret.to_dict()  # 返回字典以支持JSON序列化

    except Exception as e:
        # 错误处理
        error_ret = Output({
            "system_id": "intelligent_strategy_matching_system",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "error": True,
            "error_message": str(e),
            "error_type": type(e).__name__,
            # 策略名称（顶层单独输出）
            "strategy_name": "通用支持策略",
            "fallback_strategy": {
                "strategy_name": "通用支持策略",
                "communication_style": "温和理解、适度回应",
                "content": "我理解你现在的感受，让我们一起面对这个情况。",
                "user_type": "适应调整型",
                "emotional_state": "中性平衡"
            }
        })
        return error_ret.to_dict()  # 返回字典以支持JSON序列化
    

    
    def safe_float_convert(self, value: str, default: float = 0.0) -> float:
        """安全转换字符串为浮点数"""
        try:
            if value is None or value == '':
                return default
            return float(value)
        except (ValueError, TypeError):
            return default
    








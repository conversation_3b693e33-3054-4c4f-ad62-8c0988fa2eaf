"""
测试真实数据格式的近期情绪画像建立
"""

import asyncio
import json
from recent_emotion_profile_builder import main, Args

async def test_real_data_format():
    """测试真实数据格式"""
    
    print("=" * 70)
    print("1.3 近期情绪画像建立 - 真实数据格式测试")
    print("=" * 70)
    
    # 使用您提供的真实数据格式（简化版，取前10条）
    real_working_memory = [
        {
            "P25": "5.89",
            "P50": "6.83",
            "P75": "8.66",
            "bstudio_create_time": "2025-07-14 22:42:00 +0800 CST",
            "conversation": "你说的这个积极重构法与冲突解决技巧是什么东西呀",
            "delta_days": 1,
            "emo_value": "6",
            "emotion_change": 1,
            "emotion_deviation": "1.2199999999999998",
            "feature_score": "0.9396607142857142",
            "number": "23",
            "priority_score": "6.782180357142858",
            "quality_score": "9.6",
            "rsi_history": "0.443",
            "weight_anomaly": ""
        },
        {
            "P25": "5.89",
            "P50": "6.83",
            "P75": "8.67",
            "bstudio_create_time": "2025-07-14 22:43:09 +0800 CST",
            "conversation": "哦，听起来不错，那么就请你重点讲一下冲突解决技巧吧",
            "delta_days": 1,
            "emo_value": "5",
            "emotion_change": 2,
            "emotion_deviation": "2.2199999999999998",
            "feature_score": "1.4236607142857145",
            "number": "23",
            "priority_score": "6.580580357142858",
            "quality_score": "9.6",
            "rsi_history": "0.438",
            "weight_anomaly": ""
        },
        {
            "P25": "5.86",
            "P50": "7.21",
            "P75": "8.57",
            "bstudio_create_time": "2025-07-14 20:23:12 +0800 CST",
            "conversation": "相信我，我们一定可以的",
            "delta_days": 1,
            "emo_value": "7",
            "emotion_change": 0,
            "emotion_deviation": "0.21999999999999975",
            "feature_score": "0.4656607142857142",
            "number": "10",
            "priority_score": "6.254780357142858",
            "quality_score": "9.4",
            "rsi_history": "0.454",
            "weight_anomaly": ""
        },
        {
            "P25": "5.42",
            "P50": "6.96",
            "P75": "7.25",
            "bstudio_create_time": "2025-07-08 22:28:35 +0800 CST",
            "conversation": "今晚的星星真美呀",
            "delta_days": 7,
            "emo_value": "9",
            "emotion_change": "1.5600000000000005",
            "emotion_deviation": "1.7800000000000002",
            "feature_score": 1.093625,
            "number": "8",
            "priority_score": 6.0668625,
            "quality_score": "7.8",
            "rsi_history": "",
            "weight_anomaly": ""
        },
        {
            "P25": "5.86",
            "P50": "7.14",
            "P75": "8.58",
            "bstudio_create_time": "2025-07-14 20:25:34 +0800 CST",
            "conversation": "我们这次成功了",
            "delta_days": 1,
            "emo_value": "7",
            "emotion_change": 0,
            "emotion_deviation": "0.21999999999999975",
            "feature_score": "0.4576607142857142",
            "number": "7",
            "priority_score": "5.943980357142856",
            "quality_score": "9.0",
            "rsi_history": "0.456",
            "weight_anomaly": ""
        },
        {
            "P25": "5.89",
            "P50": "6.84",
            "P75": "8.68",
            "bstudio_create_time": "2025-07-14 20:32:55 +0800 CST",
            "conversation": "一切都好起来了",
            "delta_days": 1,
            "emo_value": "7",
            "emotion_change": 0,
            "emotion_deviation": "0.21999999999999975",
            "feature_score": "0.4576607142857142",
            "number": "7",
            "priority_score": "5.943980357142856",
            "quality_score": "9.0",
            "rsi_history": "0.46",
            "weight_anomaly": ""
        },
        {
            "P25": "5.89",
            "P50": "8.60",
            "P75": "8.66",
            "bstudio_create_time": "2025-07-11 17:05:25 +0800 CST",
            "conversation": "天晴了，雨停了，你又在哪里呢",
            "delta_days": 5,
            "emo_value": "5",
            "emotion_change": 2,
            "emotion_deviation": "2.2199999999999998",
            "feature_score": "1.3963035714285716",
            "number": "12",
            "priority_score": "5.940701785714285",
            "quality_score": "9.4",
            "rsi_history": "",
            "weight_anomaly": ""
        },
        {
            "P25": "5.92",
            "P50": "8.73",
            "P75": "8.77",
            "bstudio_create_time": "2025-07-10 13:59:05 +0800 CST",
            "conversation": "哈哈哈哈哈哈",
            "delta_days": 6,
            "emo_value": "10",
            "emotion_change": "2.5600000000000005",
            "emotion_deviation": "2.7800000000000002",
            "feature_score": "1.510964285714286",
            "number": "6",
            "priority_score": "5.9403821428571435",
            "quality_score": "7.0",
            "rsi_history": "",
            "weight_anomaly": ""
        },
        {
            "P25": "5.40",
            "P50": "6.10",
            "P75": "7.20",
            "bstudio_create_time": "2025-07-08 23:02:43 +0800 CST",
            "conversation": "有你陪我看星星真好",
            "delta_days": 7,
            "emo_value": "7",
            "emotion_change": 0,
            "emotion_deviation": "0.21999999999999975",
            "feature_score": "0.45362499999999994",
            "number": "9",
            "priority_score": 5.8728625,
            "quality_score": "8.6",
            "rsi_history": "",
            "weight_anomaly": ""
        },
        {
            "P25": "5.89",
            "P50": "6.73",
            "P75": "8.67",
            "bstudio_create_time": "2025-07-15 14:41:52 +0800 CST",
            "conversation": "哈哈哈哈哈",
            "delta_days": 1,
            "emo_value": "9",
            "emotion_change": "1.5600000000000005",
            "emotion_deviation": "1.7800000000000002",
            "feature_score": "1.0956607142857144",
            "number": "5",
            "priority_score": "5.847780357142858",
            "quality_score": "7.6",
            "rsi_history": "0.428",
            "weight_anomaly": ""
        }
    ]
    
    print(f"✅ 真实数据格式包含 {len(real_working_memory)} 条记录")
    print("数据特征分析:")
    print(f"  - 情绪值范围: {min(float(r['emo_value']) for r in real_working_memory)} - {max(float(r['emo_value']) for r in real_working_memory)}")
    print(f"  - 字数范围: {min(int(r['number']) for r in real_working_memory)} - {max(int(r['number']) for r in real_working_memory)}")
    print(f"  - 质量分数范围: {min(float(r['quality_score']) for r in real_working_memory)} - {max(float(r['quality_score']) for r in real_working_memory)}")
    
    # 测试1：基础参数（仅working_memory）
    print("\n【测试1：基础参数测试】")
    basic_params = {
        'working_memory': real_working_memory
    }
    
    args1 = Args(basic_params)
    result1 = await main(args1)
    
    if result1.get('error'):
        print(f"❌ 基础测试失败: {result1.get('error_message')}")
        print("验证结果:", result1.get('validation_result'))
    else:
        print("✅ 基础测试成功！")
        print(f"主导情绪类型: {result1.get('dominant_emotion_type')}")
        print(f"类型置信度: {result1.get('type_confidence'):.3f}")
        
        # 🎯 核心结果：近期情绪基线
        observed_baseline = result1.get('observed_baseline', {})
        print(f"\n📊 近期情绪基线（您要的核心结果）:")
        print(f"  P25: {observed_baseline.get('P25')}")
        print(f"  P50: {observed_baseline.get('P50')}")
        print(f"  P75: {observed_baseline.get('P75')}")
        
        print(f"分析置信度: {result1.get('analysis_confidence'):.3f}")
        print(f"数据数量: {result1.get('data_count')}")
    
    # 测试2：利用真实数据中的质量分数
    print("\n【测试2：利用质量分数优化权重】")
    
    # 根据quality_score生成质量权重
    quality_weights = []
    for record in real_working_memory:
        quality_score = float(record['quality_score'])
        if quality_score >= 9.0:
            quality_weights.append('A')
        elif quality_score >= 8.0:
            quality_weights.append('B')
        elif quality_score >= 7.0:
            quality_weights.append('C')
        else:
            quality_weights.append('D')
    
    # 根据weight_anomaly生成异常标记
    anomaly_flags = []
    for record in real_working_memory:
        weight_anomaly = record.get('weight_anomaly', '')
        if weight_anomaly == '':
            anomaly_flags.append('normal')
        else:
            anomaly_flags.append(weight_anomaly)
    
    enhanced_params = {
        'working_memory': real_working_memory,
        'quality_weights': quality_weights,
        'anomaly_flags': anomaly_flags,
        'weight_anomaly': 'normal'  # 全局权重正常
    }
    
    print(f"质量权重分布: A级{quality_weights.count('A')}条, B级{quality_weights.count('B')}条, C级{quality_weights.count('C')}条, D级{quality_weights.count('D')}条")
    
    args2 = Args(enhanced_params)
    result2 = await main(args2)
    
    if result2.get('error'):
        print(f"❌ 增强测试失败: {result2.get('error_message')}")
    else:
        print("✅ 增强测试成功！")
        print(f"主导情绪类型: {result2.get('dominant_emotion_type')}")
        print(f"类型置信度: {result2.get('type_confidence'):.3f}")
        
        # 🎯 核心结果对比
        observed_baseline2 = result2.get('observed_baseline', {})
        print(f"\n📊 优化后的近期情绪基线:")
        print(f"  P25: {observed_baseline2.get('P25')} (基础版: {observed_baseline.get('P25')})")
        print(f"  P50: {observed_baseline2.get('P50')} (基础版: {observed_baseline.get('P50')})")
        print(f"  P75: {observed_baseline2.get('P75')} (基础版: {observed_baseline.get('P75')})")
        
        print(f"分析置信度: {result2.get('analysis_confidence'):.3f} (提升: {result2.get('analysis_confidence') - result1.get('analysis_confidence'):.3f})")
        
        # 情绪类型得分
        print(f"\n📈 情绪类型得分分布:")
        emotion_scores = result2.get('emotion_type_scores', {})
        for emotion_type, score in emotion_scores.items():
            print(f"  {emotion_type}: {score:.3f}")
    
    # 🎯 标准化输出（给1.4节使用）
    print(f"\n【🎯 标准化输出（给1.4节贝叶斯更新使用）】")
    if not result2.get('error'):
        standardized_output = {
            "dominant_emotion_type": result2.get('dominant_emotion_type'),
            "type_confidence": result2.get('type_confidence'),
            "emotion_type_scores": result2.get('emotion_type_scores'),
            "observed_baseline": result2.get('observed_baseline'),
            "data_count": result2.get('data_count'),
            "analysis_confidence": result2.get('analysis_confidence')
        }
        print(json.dumps(standardized_output, indent=2, ensure_ascii=False))
    
    print(f"\n【✅ 真实数据测试总结】")
    print("🎯 您的数据格式完全兼容！")
    print("📊 核心输出结果:")
    if not result2.get('error'):
        print(f"  - 近期主导情绪类型: {result2.get('dominant_emotion_type')}")
        print(f"  - P25基线: {result2.get('observed_baseline', {}).get('P25')}")
        print(f"  - P50基线: {result2.get('observed_baseline', {}).get('P50')}")
        print(f"  - P75基线: {result2.get('observed_baseline', {}).get('P75')}")
        print(f"  - 分析置信度: {result2.get('analysis_confidence'):.3f}")
    
    print("🚀 代码已完全适配您的真实数据格式！")

if __name__ == "__main__":
    asyncio.run(test_real_data_format())

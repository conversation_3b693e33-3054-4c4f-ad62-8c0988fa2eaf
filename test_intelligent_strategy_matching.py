"""
智能策略匹配系统测试文件
基于方案分析报告2025.6.23-55_修改版的三层决策树架构测试
"""

import asyncio
import json
from intelligent_strategy_matching_system import main, Args

async def test_intelligent_strategy_matching():
    """测试智能策略匹配系统 - 基于三层决策树架构"""

    # 测试用例1：乐观开朗型用户，兴奋积极状态
    test_params_1 = {
        'final_P25': '6.0',
        'final_P50': '8.0',
        'final_P75': '9.0',
        'user_type': '乐观开朗型',
        'cem_value': '1.2',  # 正向动量，符合乐观开朗型特征
        'cem_grade': 'A',
        'ei_value': '1.5',   # 高强度，符合兴奋积极状态
        'ei_confidence': '0.8',
        'eii_value': '0.4',
        'rsi_value': '0.8',  # 高稳定性，符合乐观开朗型特征
        'final_conf_score': '0.8',
        'crisis_probability': '0.2',  # 低危机，符合乐观开朗型特征
        'health_score': '0.8',
        'trend_prediction': 'positive'
    }

    # 测试用例2：悲观消极型用户，危机焦虑状态
    test_params_2 = {
        'final_P25': '3.0',
        'final_P50': '4.5',
        'final_P75': '6.0',
        'user_type': '悲观消极型',
        'cem_value': '-1.2',  # 负向动量，符合悲观消极型特征
        'cem_grade': 'D',
        'ei_value': '1.3',    # 高强度表达
        'ei_confidence': '0.9',
        'eii_value': '0.8',
        'rsi_value': '0.4',   # 中低稳定性，符合悲观消极型特征
        'final_conf_score': '0.7',
        'crisis_probability': '0.8',  # 高危机，符合悲观消极型特征
        'health_score': '0.3',
        'trend_prediction': 'negative'
    }

    # 测试用例3：沉稳内敛型用户，平静稳定状态
    test_params_3 = {
        'final_P25': '5.5',
        'final_P50': '7.0',
        'final_P75': '8.5',
        'user_type': '沉稳内敛型',
        'cem_value': '0.1',   # 低动量，符合沉稳内敛型特征
        'cem_grade': 'B',
        'ei_value': '0.4',    # 低强度，符合平静稳定状态
        'ei_confidence': '0.6',
        'eii_value': '0.8',   # 高惯性，符合沉稳内敛型特征
        'rsi_value': '0.9',   # 高稳定性，符合沉稳内敛型特征
        'final_conf_score': '0.8',
        'crisis_probability': '0.3',  # 低危机，符合沉稳内敛型特征
        'health_score': '0.7',
        'trend_prediction': 'stable'
    }

    # 测试用例4：情绪敏感型用户，沮丧消极状态
    test_params_4 = {
        'final_P25': '4.0',
        'final_P50': '5.5',
        'final_P75': '7.0',
        'user_type': '情绪敏感型',
        'cem_value': '-0.8',  # 负向动量
        'cem_grade': 'C',
        'ei_value': '1.6',    # 高强度，符合情绪敏感型特征
        'ei_confidence': '0.9',
        'eii_value': '0.6',
        'rsi_value': '0.5',
        'final_conf_score': '0.8',
        'crisis_probability': '0.5',  # 中等危机，符合情绪敏感型特征
        'health_score': '0.4',
        'trend_prediction': 'stable'
    }

    # 测试用例5：适应调整型用户，中性平衡状态
    test_params_5 = {
        'final_P25': '5.0',
        'final_P50': '6.5',
        'final_P75': '8.0',
        'user_type': '适应调整型',
        'cem_value': '0.0',   # 中性动量
        'cem_grade': 'C',
        'ei_value': '0.8',    # 中等强度
        'ei_confidence': '0.6',
        'eii_value': '0.5',
        'rsi_value': '0.6',
        'final_conf_score': '0.5',  # 低置信度，符合适应调整型特征
        'crisis_probability': '0.4',
        'health_score': '0.6',
        'trend_prediction': 'stable'
    }

    test_cases = [
        ("乐观开朗型-兴奋积极", test_params_1),
        ("悲观消极型-危机焦虑", test_params_2),
        ("沉稳内敛型-平静稳定", test_params_3),
        ("情绪敏感型-沮丧消极", test_params_4),
        ("适应调整型-中性平衡", test_params_5)
    ]

    for case_name, params in test_cases:
        print(f"\n{'='*60}")
        print(f"测试用例: {case_name}")
        print(f"{'='*60}")

        args = Args(params)
        result = await main(args)

        # 输出三层决策树结果
        print(f"\n【第一层：用户类型识别】")
        user_type_result = result['user_type_identification']
        print(f"识别类型: {user_type_result['identified_type']}")
        print(f"类型置信度: {user_type_result['type_confidence']:.3f}")

        print(f"\n【第二层：情绪状态评估】")
        emotion_result = result['emotional_state_evaluation']
        print(f"当前状态: {emotion_result['current_state']}")
        print(f"状态置信度: {emotion_result['state_confidence']:.3f}")
        print(f"优先级等级: {emotion_result['priority_level']}")

        print(f"\n【第三层：策略内容生成】")
        strategy_result = result['strategy_content_generation']
        print(f"策略名称: {strategy_result['strategy_name']}")
        print(f"沟通风格: {strategy_result['communication_style']}")
        print(f"内容示例: {strategy_result['content']}")
        print(f"强度修饰: {strategy_result['intensity_modifier']}")
        print(f"关系语境: {strategy_result['relationship_context']}")
        print(f"时间语境: {strategy_result['time_context']}")
        print(f"支持强度: {strategy_result['support_level']}")

        print(f"\n【个性化因子详情】")
        personalization = strategy_result['personalization_factors']
        print(f"CEM等级稳定性: {personalization['cem_grade_stability']}")
        print(f"EI置信度水平: {personalization['ei_confidence_level']:.3f}")
        print(f"健康支持需求: {personalization['health_support_need']:.3f}")
        print(f"趋势展望: {personalization['trend_outlook']}")

        print(f"\n【综合匹配结果】")
        matching_result = result['matching_results']
        print(f"综合置信度: {matching_result['overall_confidence']:.3f}")
        print(f"决策路径: {matching_result['decision_path']}")
        print(f"个性化程度: 强度={matching_result['content_personalization']['intensity_level']}, 关系={matching_result['content_personalization']['relationship_context']}")

        print(f"\n【质量控制指标】")
        quality_result = result['quality_metrics']
        print(f"输入验证: {'通过' if quality_result['input_validation']['is_valid'] else '失败'}")
        if quality_result['input_validation']['warnings']:
            print("验证警告:")
            for warning in quality_result['input_validation']['warnings']:
                print(f"  - {warning}")
        print(f"决策可靠性: {quality_result['decision_reliability']}")
        confidence_breakdown = quality_result['confidence_breakdown']
        print(f"置信度分解: 类型={confidence_breakdown['type_confidence']:.3f}, 状态={confidence_breakdown['state_confidence']:.3f}, 内容={confidence_breakdown['content_confidence']:.3f}")

if __name__ == "__main__":
    asyncio.run(test_intelligent_strategy_matching())

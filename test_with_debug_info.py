"""
使用调试信息测试您的数据
"""

import asyncio
import json
from recent_emotion_profile_builder import main, Args, validate_input_parameters

async def test_with_debug_info():
    """使用调试信息测试您的数据"""
    
    print("=" * 80)
    print("1.3 近期情绪画像建立 - 调试信息测试")
    print("=" * 80)
    
    # 您提供的确切数据（前5条）
    your_data = {
        "working_memory": [
            {
                "P25": "5.89",
                "P50": "6.83",
                "P75": "8.66",
                "bstudio_create_time": "2025-07-14 22:42:00 +0800 CST",
                "conversation": "你说的这个积极重构法与冲突解决技巧是什么东西呀",
                "delta_days": 1,
                "emo_value": "6",
                "emotion_change": "-0.5600000000000005",
                "emotion_deviation": "1.2199999999999998",
                "feature_score": "0.6276607142857141",
                "number": "23",
                "priority_score": "6.750980357142858",
                "quality_score": "9.6",
                "rsi_history": "0.443",
                "weight_anomaly": ""
            },
            {
                "P25": "5.89",
                "P50": "6.83",
                "P75": "8.67",
                "bstudio_create_time": "2025-07-14 22:43:09 +0800 CST",
                "conversation": "哦，听起来不错，那么就请你重点讲一下冲突解决技巧吧",
                "delta_days": 1,
                "emo_value": "5",
                "emotion_change": "0.4399999999999995",
                "emotion_deviation": "2.2199999999999998",
                "feature_score": "1.1116607142857142",
                "number": "23",
                "priority_score": "6.549380357142858",
                "quality_score": "9.6",
                "rsi_history": "0.438",
                "weight_anomaly": ""
            },
            {
                "P25": "5.86",
                "P50": "7.21",
                "P75": "8.57",
                "bstudio_create_time": "2025-07-14 20:23:12 +0800 CST",
                "conversation": "相信我，我们一定可以的",
                "delta_days": 1,
                "emo_value": "7",
                "emotion_change": "-1.5600000000000005",
                "emotion_deviation": "0.21999999999999975",
                "feature_score": "0.1536607142857141",
                "number": "10",
                "priority_score": "6.223580357142858",
                "quality_score": "9.4",
                "rsi_history": "0.454",
                "weight_anomaly": ""
            },
            {
                "P25": "5.42",
                "P50": "6.96",
                "P75": "7.25",
                "bstudio_create_time": "2025-07-08 22:28:35 +0800 CST",
                "conversation": "今晚的星星真美呀",
                "delta_days": 7,
                "emo_value": "9",
                "emotion_change": 0,
                "emotion_deviation": "1.7800000000000002",
                "feature_score": "0.7816250000000001",
                "number": "8",
                "priority_score": "6.035662500000001",
                "quality_score": "7.8",
                "rsi_history": "",
                "weight_anomaly": ""
            },
            {
                "P25": "5.86",
                "P50": "7.14",
                "P75": "8.58",
                "bstudio_create_time": "2025-07-14 20:25:34 +0800 CST",
                "conversation": "我们这次成功了",
                "delta_days": 1,
                "emo_value": "7",
                "emotion_change": "-1.5600000000000005",
                "emotion_deviation": "0.21999999999999975",
                "feature_score": "0.14566071428571412",
                "number": "7",
                "priority_score": "5.912780357142857",
                "quality_score": "9.0",
                "rsi_history": "0.456",
                "weight_anomaly": ""
            }
        ]
    }
    
    print("【步骤1：数据基本信息】")
    print(f"输入数据类型: {type(your_data)}")
    print(f"working_memory长度: {len(your_data['working_memory'])}")
    print(f"第一条记录的必需字段:")
    first_record = your_data['working_memory'][0]
    required_fields = ['emo_value', 'number', 'bstudio_create_time']
    for field in required_fields:
        print(f"  {field}: {first_record.get(field, '缺失')}")
    
    print("\n【步骤2：验证函数测试】")
    validation_result = validate_input_parameters(your_data)
    print("验证结果:")
    print(f"  is_valid: {validation_result['is_valid']}")
    print(f"  missing_params: {validation_result['missing_params']}")
    print(f"  warnings: {validation_result['warnings']}")
    
    if 'debug_info' in validation_result:
        print("调试信息:")
        debug_info = validation_result['debug_info']
        for key, value in debug_info.items():
            print(f"  {key}: {value}")
    
    print("\n【步骤3：Args类测试】")
    try:
        args = Args(your_data)
        print(f"Args创建成功")
        print(f"args.params类型: {type(args.params)}")
        print(f"args.params中working_memory长度: {len(args.params.get('working_memory', []))}")
    except Exception as e:
        print(f"Args创建失败: {e}")
        return
    
    print("\n【步骤4：主函数调用测试】")
    try:
        result = await main(args)
        
        if result.get('error'):
            print("❌ 主函数调用失败:")
            print(f"  错误消息: {result.get('error_message')}")
            print(f"  错误类型: {result.get('error_type')}")
            
            # 显示验证结果
            if 'validation_result' in result:
                val_result = result['validation_result']
                print("  验证详情:")
                print(f"    is_valid: {val_result.get('is_valid')}")
                print(f"    missing_params: {val_result.get('missing_params', [])}")
                print(f"    warnings数量: {len(val_result.get('warnings', []))}")
                
                if 'debug_info' in val_result:
                    print("  调试信息:")
                    for key, value in val_result['debug_info'].items():
                        print(f"    {key}: {value}")
        else:
            print("✅ 主函数调用成功!")
            print(f"主导情绪类型: {result.get('dominant_emotion_type')}")
            print(f"类型置信度: {result.get('type_confidence')}")
            
            # 显示核心结果
            baseline = result.get('observed_baseline', {})
            print(f"\n📊 近期情绪基线（符合1.3节要求）:")
            print(f"  P25: {baseline.get('P25')}")
            print(f"  P50: {baseline.get('P50')}")
            print(f"  P75: {baseline.get('P75')}")
            
            print(f"\n📈 分析质量:")
            print(f"  数据数量: {result.get('data_count')}")
            print(f"  分析置信度: {result.get('analysis_confidence')}")
            
            # 验证输出格式是否符合1.3节要求
            print(f"\n【验证输出格式符合1.3节要求】")
            required_output_fields = [
                'dominant_emotion_type', 'type_confidence', 'emotion_type_scores',
                'observed_baseline', 'data_count', 'analysis_confidence'
            ]
            
            missing_fields = []
            for field in required_output_fields:
                if field not in result:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ 缺少必需输出字段: {missing_fields}")
            else:
                print("✅ 输出格式完全符合1.3节要求")
                
                # 显示标准化输出
                standardized_output = {
                    "dominant_emotion_type": result.get('dominant_emotion_type'),
                    "type_confidence": result.get('type_confidence'),
                    "emotion_type_scores": result.get('emotion_type_scores'),
                    "observed_baseline": result.get('observed_baseline'),
                    "data_count": result.get('data_count'),
                    "analysis_confidence": result.get('analysis_confidence')
                }
                print("\n📋 标准化输出（给1.4节贝叶斯更新使用）:")
                print(json.dumps(standardized_output, indent=2, ensure_ascii=False))
                
    except Exception as e:
        print(f"❌ 主函数调用异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n【总结】")
    print("🔍 如果仍然出现'working_memory为空'错误，问题可能在于:")
    print("1. 实际调用时传入的参数与测试数据不同")
    print("2. JSON解析过程中数据丢失")
    print("3. 异步函数调用环境问题")
    print("4. 文件导入路径问题")

if __name__ == "__main__":
    asyncio.run(test_with_debug_info())

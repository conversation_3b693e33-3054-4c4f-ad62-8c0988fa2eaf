"""
测试您提供的确切输入数据
"""

import asyncio
import json
from recent_emotion_profile_builder import main, Args

async def test_your_exact_input():
    """测试您提供的确切输入数据"""
    
    print("=" * 70)
    print("测试您提供的确切输入数据")
    print("=" * 70)
    
    # 您提供的确切输入数据（前5条作为测试）
    your_input = {
        "working_memory": [
            {
                "P25": "5.89",
                "P50": "6.83",
                "P75": "8.66",
                "bstudio_create_time": "2025-07-14 22:42:00 +0800 CST",
                "conversation": "你说的这个积极重构法与冲突解决技巧是什么东西呀",
                "delta_days": 1,
                "emo_value": "6",
                "emotion_change": "-0.5600000000000005",
                "emotion_deviation": "1.2199999999999998",
                "feature_score": "0.6276607142857141",
                "number": "23",
                "priority_score": "6.750980357142858",
                "quality_score": "9.6",
                "rsi_history": "0.443",
                "weight_anomaly": ""
            },
            {
                "P25": "5.89",
                "P50": "6.83",
                "P75": "8.67",
                "bstudio_create_time": "2025-07-14 22:43:09 +0800 CST",
                "conversation": "哦，听起来不错，那么就请你重点讲一下冲突解决技巧吧",
                "delta_days": 1,
                "emo_value": "5",
                "emotion_change": "0.4399999999999995",
                "emotion_deviation": "2.2199999999999998",
                "feature_score": "1.1116607142857142",
                "number": "23",
                "priority_score": "6.549380357142858",
                "quality_score": "9.6",
                "rsi_history": "0.438",
                "weight_anomaly": ""
            },
            {
                "P25": "5.86",
                "P50": "7.21",
                "P75": "8.57",
                "bstudio_create_time": "2025-07-14 20:23:12 +0800 CST",
                "conversation": "相信我，我们一定可以的",
                "delta_days": 1,
                "emo_value": "7",
                "emotion_change": "-1.5600000000000005",
                "emotion_deviation": "0.21999999999999975",
                "feature_score": "0.1536607142857141",
                "number": "10",
                "priority_score": "6.223580357142858",
                "quality_score": "9.4",
                "rsi_history": "0.454",
                "weight_anomaly": ""
            },
            {
                "P25": "5.42",
                "P50": "6.96",
                "P75": "7.25",
                "bstudio_create_time": "2025-07-08 22:28:35 +0800 CST",
                "conversation": "今晚的星星真美呀",
                "delta_days": 7,
                "emo_value": "9",
                "emotion_change": 0,
                "emotion_deviation": "1.7800000000000002",
                "feature_score": "0.7816250000000001",
                "number": "8",
                "priority_score": "6.035662500000001",
                "quality_score": "7.8",
                "rsi_history": "",
                "weight_anomaly": ""
            },
            {
                "P25": "5.86",
                "P50": "7.14",
                "P75": "8.58",
                "bstudio_create_time": "2025-07-14 20:25:34 +0800 CST",
                "conversation": "我们这次成功了",
                "delta_days": 1,
                "emo_value": "7",
                "emotion_change": "-1.5600000000000005",
                "emotion_deviation": "0.21999999999999975",
                "feature_score": "0.14566071428571412",
                "number": "7",
                "priority_score": "5.912780357142857",
                "quality_score": "9.0",
                "rsi_history": "0.456",
                "weight_anomaly": ""
            }
        ]
    }
    
    print(f"✅ 输入数据包含 {len(your_input['working_memory'])} 条记录")
    
    # 检查数据结构
    print("\n【数据结构检查】")
    first_record = your_input['working_memory'][0]
    required_fields = ['emo_value', 'number', 'bstudio_create_time']
    
    print("必需字段检查:")
    for field in required_fields:
        if field in first_record:
            print(f"  ✅ {field}: {first_record[field]}")
        else:
            print(f"  ❌ {field}: 缺失")
    
    print(f"\n数据特征:")
    emo_values = [float(r['emo_value']) for r in your_input['working_memory']]
    numbers = [int(r['number']) for r in your_input['working_memory']]
    print(f"  - 情绪值范围: {min(emo_values)} - {max(emo_values)}")
    print(f"  - 字数范围: {min(numbers)} - {max(numbers)}")
    
    # 测试调用
    print("\n【测试函数调用】")
    try:
        args = Args(your_input)
        result = await main(args)
        
        if result.get('error'):
            print(f"❌ 调用失败:")
            print(f"  错误消息: {result.get('error_message')}")
            print(f"  错误类型: {result.get('error_type')}")
            
            # 详细验证结果
            validation = result.get('validation_result', {})
            if validation:
                print(f"  验证状态: {validation.get('is_valid')}")
                print(f"  缺失参数: {validation.get('missing_params', [])}")
                print(f"  警告数量: {len(validation.get('warnings', []))}")
                
                if validation.get('warnings'):
                    print("  警告详情:")
                    for warning in validation.get('warnings', []):
                        print(f"    - {warning}")
        else:
            print("✅ 调用成功！")
            print(f"主导情绪类型: {result.get('dominant_emotion_type')}")
            print(f"类型置信度: {result.get('type_confidence')}")
            
            # 核心结果
            baseline = result.get('observed_baseline', {})
            print(f"\n📊 近期情绪基线:")
            print(f"  P25: {baseline.get('P25')}")
            print(f"  P50: {baseline.get('P50')}")
            print(f"  P75: {baseline.get('P75')}")
            
            print(f"\n📈 分析质量:")
            print(f"  数据数量: {result.get('data_count')}")
            print(f"  分析置信度: {result.get('analysis_confidence')}")
            
            # 标准化输出
            print(f"\n【标准化输出】")
            standardized = {
                "dominant_emotion_type": result.get('dominant_emotion_type'),
                "type_confidence": result.get('type_confidence'),
                "emotion_type_scores": result.get('emotion_type_scores'),
                "observed_baseline": result.get('observed_baseline'),
                "data_count": result.get('data_count'),
                "analysis_confidence": result.get('analysis_confidence')
            }
            print(json.dumps(standardized, indent=2, ensure_ascii=False))
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 可能的问题原因分析
    print(f"\n【可能的问题原因分析】")
    print("如果您看到错误 'working_memory为空'，可能的原因:")
    print("1. 🔍 输入参数传递时格式不正确")
    print("2. 🔍 JSON解析过程中数据丢失")
    print("3. 🔍 Args类初始化时参数处理有问题")
    print("4. 🔍 函数调用环境中的参数传递问题")
    
    print(f"\n【解决建议】")
    print("1. ✅ 确保输入数据格式完全按照上面的结构")
    print("2. ✅ 检查JSON字符串是否完整且格式正确")
    print("3. ✅ 确认Args类能正确接收参数")
    print("4. ✅ 验证main函数能正确访问working_memory")

if __name__ == "__main__":
    asyncio.run(test_your_exact_input())

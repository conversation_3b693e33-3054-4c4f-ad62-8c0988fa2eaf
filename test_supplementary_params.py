"""
测试补充参数的效果对比
"""

import asyncio
import json
from intelligent_strategy_matching_system import main, Args

async def test_supplementary_params_comparison():
    """对比有无补充参数的效果"""
    
    # 基础参数（不包含补充参数）
    base_params = {
        'final_P25': '6.0',
        'final_P50': '8.0',
        'final_P75': '9.0',
        'user_type': '乐观开朗型',
        'cem_value': '1.2',
        'ei_value': '1.5',
        'eii_value': '0.4',
        'rsi_value': '0.8',
        'final_conf_score': '0.8',
        'crisis_probability': '0.2'
    }
    
    # 完整参数（包含补充参数）
    full_params = {
        **base_params,
        'cem_grade': 'A',           # 补充参数1
        'ei_confidence': '0.9',     # 补充参数2
        'health_score': '0.8',      # 补充参数3
        'trend_prediction': 'positive'  # 补充参数4
    }
    
    print("=" * 80)
    print("智能策略匹配系统 - 补充参数效果对比测试")
    print("=" * 80)
    
    # 测试1：基础参数
    print("\n【测试1：仅基础参数】")
    args1 = Args(base_params)
    result1 = await main(args1)
    
    print(f"用户类型识别: {result1['user_type_identification']['identified_type']}")
    print(f"类型置信度: {result1['user_type_identification']['type_confidence']:.3f}")
    print(f"情绪状态评估: {result1['emotional_state_evaluation']['current_state']}")
    print(f"状态置信度: {result1['emotional_state_evaluation']['state_confidence']:.3f}")
    print(f"综合置信度: {result1['matching_results']['overall_confidence']:.3f}")
    
    strategy1 = result1['strategy_content_generation']
    print(f"策略名称: {strategy1['strategy_name']}")
    print(f"强度修饰: {strategy1['intensity_modifier']}")
    print(f"关系语境: {strategy1['relationship_context']}")
    
    validation1 = result1['quality_metrics']['input_validation']
    print(f"验证警告数量: {len(validation1['warnings'])}")
    
    # 测试2：完整参数
    print("\n【测试2：包含补充参数】")
    args2 = Args(full_params)
    result2 = await main(args2)
    
    print(f"用户类型识别: {result2['user_type_identification']['identified_type']}")
    print(f"类型置信度: {result2['user_type_identification']['type_confidence']:.3f}")
    print(f"情绪状态评估: {result2['emotional_state_evaluation']['current_state']}")
    print(f"状态置信度: {result2['emotional_state_evaluation']['state_confidence']:.3f}")
    print(f"综合置信度: {result2['matching_results']['overall_confidence']:.3f}")
    
    strategy2 = result2['strategy_content_generation']
    print(f"策略名称: {strategy2['strategy_name']}")
    print(f"强度修饰: {strategy2['intensity_modifier']}")
    print(f"关系语境: {strategy2['relationship_context']}")
    print(f"时间语境: {strategy2['time_context']}")
    print(f"支持强度: {strategy2['support_level']}")
    
    # 个性化因子详情
    personalization = strategy2['personalization_factors']
    print(f"CEM等级稳定性: {personalization['cem_grade_stability']}")
    print(f"EI置信度水平: {personalization['ei_confidence_level']:.3f}")
    print(f"健康支持需求: {personalization['health_support_need']:.3f}")
    print(f"趋势展望: {personalization['trend_outlook']}")
    
    validation2 = result2['quality_metrics']['input_validation']
    print(f"验证警告数量: {len(validation2['warnings'])}")
    
    # 效果对比分析
    print("\n【效果对比分析】")
    print(f"类型置信度提升: {result2['user_type_identification']['type_confidence'] - result1['user_type_identification']['type_confidence']:.3f}")
    print(f"状态置信度提升: {result2['emotional_state_evaluation']['state_confidence'] - result1['emotional_state_evaluation']['state_confidence']:.3f}")
    print(f"综合置信度提升: {result2['matching_results']['overall_confidence'] - result1['matching_results']['overall_confidence']:.3f}")
    print(f"验证警告减少: {len(validation1['warnings']) - len(validation2['warnings'])} 个")
    
    # 个性化程度对比
    print(f"\n【个性化程度对比】")
    print("基础参数版本:")
    print(f"  - 强度修饰: {strategy1['intensity_modifier']}")
    print(f"  - 关系语境: {strategy1['relationship_context']}")
    print("完整参数版本:")
    print(f"  - 强度修饰: {strategy2['intensity_modifier']}")
    print(f"  - 关系语境: {strategy2['relationship_context']}")
    print(f"  - 时间语境: {strategy2['time_context']}")
    print(f"  - 支持强度: {strategy2['support_level']}")
    
    print("\n【结论】")
    if result2['matching_results']['overall_confidence'] > result1['matching_results']['overall_confidence']:
        print("✅ 补充参数显著提升了匹配精度和个性化程度")
    else:
        print("⚠️  补充参数对匹配精度的提升有限")
    
    print(f"建议：{'强烈推荐' if len(validation2['warnings']) == 0 else '建议'}提供所有补充参数以获得最佳匹配效果")

if __name__ == "__main__":
    asyncio.run(test_supplementary_params_comparison())

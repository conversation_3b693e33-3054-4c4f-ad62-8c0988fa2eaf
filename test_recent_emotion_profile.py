"""
测试1.3近期情绪画像建立功能
"""

import asyncio
import json
from datetime import datetime, timedelta
from recent_emotion_profile_builder import main, Args

async def test_recent_emotion_profile():
    """测试近期情绪画像建立功能"""
    
    print("=" * 70)
    print("1.3 近期情绪画像建立 - 功能测试")
    print("=" * 70)
    
    # 构造测试数据：模拟7天内的用户交互数据
    base_time = datetime.now() - timedelta(days=7)
    working_memory = []
    
    # 生成25条测试记录（充足数据量）
    for i in range(25):
        record_time = base_time + timedelta(hours=i*6, minutes=i*10)
        
        # 模拟乐观开朗型用户的数据特征
        if i < 15:  # 前15条：乐观开朗型特征
            emo_value = str(7.0 + (i % 3) * 0.5)  # 6.5-8.0范围
            number = str(20 + (i % 10) * 2)       # 20-40字数范围
        else:  # 后10条：轻微波动
            emo_value = str(6.0 + (i % 4) * 0.3)  # 6.0-6.9范围
            number = str(15 + (i % 8) * 3)        # 15-36字数范围
        
        working_memory.append({
            'emo_value': emo_value,
            'number': number,
            'bstudio_create_time': record_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 模拟长期画像数据
    long_term_profile = {
        'dominant_emotion_type': '乐观开朗型',
        'type_confidence': 0.75,
        'S_P25': 6.8,
        'S_P50': 7.5,
        'S_P75': 8.2
    }
    
    # 模拟质量权重（大部分B级，少数A级和C级）
    quality_weights = ['B'] * 20 + ['A'] * 3 + ['C'] * 2
    
    # 模拟异常标记（大部分正常，少数轻微异常）
    anomaly_flags = ['normal'] * 22 + ['light'] * 3
    
    # 测试用例1：完整参数测试
    print("\n【测试用例1：完整参数测试】")
    test_params_1 = {
        'working_memory': working_memory,
        'long_term_profile': long_term_profile,
        'quality_weights': quality_weights,
        'anomaly_flags': anomaly_flags
    }
    
    args1 = Args(test_params_1)
    result1 = await main(args1)
    
    if result1.get('error'):
        print(f"❌ 测试失败: {result1.get('error_message')}")
    else:
        print("✅ 测试成功")
        print(f"主导情绪类型: {result1.get('dominant_emotion_type')}")
        print(f"类型置信度: {result1.get('type_confidence')}")
        print(f"分析置信度: {result1.get('analysis_confidence')}")
        print(f"数据充分性: {result1['quality_metrics']['data_sufficiency_level']}")
        print(f"分析可靠性: {result1['quality_metrics']['analysis_reliability']}")
        
        # 显示观测基线
        baseline = result1.get('observed_baseline', {})
        print(f"观测基线: P25={baseline.get('P25')}, P50={baseline.get('P50')}, P75={baseline.get('P75')}")
        
        # 显示各类型得分
        print("情绪类型得分:")
        for emotion_type, score in result1.get('emotion_type_scores', {}).items():
            print(f"  {emotion_type}: {score:.3f}")
    
    # 测试用例2：最少参数测试
    print("\n【测试用例2：最少参数测试（仅working_memory）】")
    test_params_2 = {
        'working_memory': working_memory[:8]  # 只提供8条数据
    }
    
    args2 = Args(test_params_2)
    result2 = await main(args2)
    
    if result2.get('error'):
        print(f"❌ 测试失败: {result2.get('error_message')}")
    else:
        print("✅ 测试成功")
        print(f"主导情绪类型: {result2.get('dominant_emotion_type')}")
        print(f"数据充分性: {result2['quality_metrics']['data_sufficiency_level']}")
        print(f"验证警告数量: {len(result2['quality_metrics']['input_validation']['warnings'])}")
    
    # 测试用例3：数据不足测试
    print("\n【测试用例3：数据不足测试】")
    test_params_3 = {
        'working_memory': working_memory[:3]  # 只提供3条数据
    }
    
    args3 = Args(test_params_3)
    result3 = await main(args3)
    
    if result3.get('error'):
        print(f"⚠️  预期的数据不足错误: {result3.get('error_message')}")
    else:
        print("✅ 系统容错处理正常")
        print(f"数据充分性: {result3['quality_metrics']['data_sufficiency_level']}")
    
    # 测试用例4：错误数据测试
    print("\n【测试用例4：错误数据格式测试】")
    test_params_4 = {
        'working_memory': [
            {'emo_value': 'invalid', 'number': '20', 'bstudio_create_time': '2024-01-01 12:00:00'},
            {'emo_value': '7.0', 'number': 'invalid', 'bstudio_create_time': '2024-01-01 13:00:00'}
        ]
    }
    
    args4 = Args(test_params_4)
    result4 = await main(args4)
    
    if result4.get('error'):
        print(f"⚠️  预期的数据格式错误: {result4.get('error_message')}")
    else:
        print("✅ 系统容错处理正常，自动转换无效数据为默认值")
    
    # 输出标准化结果示例
    print("\n【标准化输出示例（给1.4节贝叶斯更新使用）】")
    if not result1.get('error'):
        standardized_output = {
            "dominant_emotion_type": result1.get('dominant_emotion_type'),
            "type_confidence": result1.get('type_confidence'),
            "emotion_type_scores": result1.get('emotion_type_scores'),
            "observed_baseline": result1.get('observed_baseline'),
            "data_count": result1.get('data_count'),
            "analysis_confidence": result1.get('analysis_confidence')
        }
        print(json.dumps(standardized_output, indent=2, ensure_ascii=False))
    
    print("\n【功能验证总结】")
    print("✅ 数据验证与预处理功能正常")
    print("✅ S/M/T参数计算功能正常")
    print("✅ 情绪类型匹配功能正常")
    print("✅ 多维度置信度评估功能正常")
    print("✅ 标准化输出格式正确")
    print("✅ 错误处理和容错机制完善")
    print("✅ 符合1.3节方案要求的所有功能点")

if __name__ == "__main__":
    asyncio.run(test_recent_emotion_profile())

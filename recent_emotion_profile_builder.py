"""
1.3 近期情绪画像建立
基于工作记忆层数据建立用户近期情绪画像，计算标准化的近期情绪类型得分和置信度
适配Python 3.11.3标准库环境
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional

# 输入输出类型定义
class Args:
    def __init__(self, params: Dict[str, Any]):
        self.params = params

class Output:
    def __init__(self, data: Dict[str, Any]):
        self.data = data

    def __getitem__(self, key):
        return self.data[key]

    def __setitem__(self, key, value):
        self.data[key] = value
    
    def to_dict(self) -> Dict[str, Any]:
        return self.data

class RecentEmotionProfileBuilder:
    """近期情绪画像建立核心引擎"""
    
    def __init__(self):
        # 五大情绪类型特征参数
        self.emotion_type_features = {
            '乐观开朗型': {
                'S_range': (6.5, 10.0),
                'M_range': (15, 50),
                'T_median_range': (30, 180)
            },
            '悲观消极型': {
                'S_range': (1.0, 4.5),
                'M_range': (5, 25),
                'T_median_range': (300, 1800)
            },
            '沉稳内敛型': {
                'S_range': (4.0, 7.0),
                'M_range': (8, 30),
                'T_median_range': (120, 600)
            },
            '情绪敏感型': {
                'S_range': (3.0, 8.0),
                'M_range': (20, 80),
                'T_median_range': (10, 120)
            },
            '适应调整型': {
                'S_range': (4.5, 7.5),
                'M_range': (10, 40),
                'T_median_range': (60, 300)
            }
        }
        
        # 权重配置
        self.type_weights = {
            'S_weight': 0.6,  # 情绪分主导因子
            'M_weight': 0.25, # 字数调节因子
            'T_weight': 0.15  # 时间背景因子
        }
        
        # 质量系数
        self.quality_coefficients = {
            'A': 1.0,
            'B': 0.8,
            'C': 0.5,
            'D': 0.1
        }
        
        # 异常值权重调整
        self.anomaly_adjustments = {
            'normal': 1.0,
            'light': 0.7,
            'moderate': 0.3,
            'severe': 0.0  # 排除计算
        }
    
    def safe_float_convert(self, value: str, default: float = 0.0) -> float:
        """安全转换字符串为浮点数"""
        try:
            if value is None or value == '':
                return default
            return float(value)
        except (ValueError, TypeError):
            return default
    
    def parse_datetime(self, time_str: str) -> Optional[datetime]:
        """解析时间字符串"""
        try:
            # 预处理：移除时区信息
            if '+0800 CST' in time_str:
                time_str = time_str.replace(' +0800 CST', '')
            elif '+0800' in time_str:
                time_str = time_str.replace(' +0800', '')
            elif 'CST' in time_str:
                time_str = time_str.replace(' CST', '')

            # 支持多种时间格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S.%f'
            ]
            for fmt in formats:
                try:
                    return datetime.strptime(time_str, fmt)
                except ValueError:
                    continue
            return None
        except Exception:
            return None
    
    def validate_and_preprocess_data(self, working_memory: List[Dict],
                                   quality_weights: List[str] = None,
                                   anomaly_flags: List[str] = None,
                                   weight_anomaly: str = None) -> Tuple[List[Dict], float]:
        """数据验证与预处理"""
        if not working_memory:
            return [], 0.0
        
        # 默认质量权重和异常标记
        if quality_weights is None:
            quality_weights = ['B'] * len(working_memory)
        if anomaly_flags is None:
            anomaly_flags = ['normal'] * len(working_memory)

        # 处理全局权重异常标记
        global_anomaly_adjustment = 1.0
        if weight_anomaly:
            global_anomaly_adjustment = self.anomaly_adjustments.get(weight_anomaly, 1.0)
        
        processed_data = []
        total_weight = 0.0
        
        for i, record in enumerate(working_memory):
            # 提取基础数据
            emo_value = self.safe_float_convert(record.get('emo_value', '0'))
            number = self.safe_float_convert(record.get('number', '0'))
            create_time = self.parse_datetime(record.get('bstudio_create_time', ''))
            
            if create_time is None:
                continue
            
            # 计算有效权重
            quality_grade = quality_weights[i] if i < len(quality_weights) else 'B'
            anomaly_level = anomaly_flags[i] if i < len(anomaly_flags) else 'normal'
            
            quality_coeff = self.quality_coefficients.get(quality_grade, 0.8)
            anomaly_coeff = self.anomaly_adjustments.get(anomaly_level, 1.0)
            
            effective_weight = quality_coeff * anomaly_coeff * global_anomaly_adjustment

            # 排除严重异常数据
            if effective_weight <= 0:
                continue
            
            processed_record = {
                'emo_value': emo_value,
                'number': number,
                'create_time': create_time,
                'effective_weight': effective_weight,
                'quality_grade': quality_grade,
                'anomaly_level': anomaly_level
            }
            
            processed_data.append(processed_record)
            total_weight += effective_weight
        
        return processed_data, total_weight
    
    def calculate_weighted_percentile(self, values: List[float], weights: List[float], percentile: float) -> float:
        """计算加权分位数"""
        if not values or not weights:
            return 0.0
        
        # 使用numpy进行加权分位数计算
        values_array = np.array(values)
        weights_array = np.array(weights)
        
        # 排序
        sorted_indices = np.argsort(values_array)
        sorted_values = values_array[sorted_indices]
        sorted_weights = weights_array[sorted_indices]
        
        # 计算累积权重
        cumulative_weights = np.cumsum(sorted_weights)
        total_weight = cumulative_weights[-1]
        
        # 找到分位数位置
        target_weight = total_weight * percentile
        
        # 线性插值
        for i in range(len(cumulative_weights)):
            if cumulative_weights[i] >= target_weight:
                if i == 0:
                    return sorted_values[0]
                
                # 线性插值
                weight_diff = cumulative_weights[i] - cumulative_weights[i-1]
                value_diff = sorted_values[i] - sorted_values[i-1]
                weight_offset = target_weight - cumulative_weights[i-1]
                
                return sorted_values[i-1] + (weight_offset / weight_diff) * value_diff
        
        return sorted_values[-1]
    
    def calculate_weighted_stats(self, values: List[float], weights: List[float]) -> Dict[str, float]:
        """计算加权统计量"""
        if not values or not weights:
            return {'mean': 0.0, 'std': 0.0, 'median': 0.0}
        
        values_array = np.array(values)
        weights_array = np.array(weights)
        
        # 加权平均
        weighted_mean = np.average(values_array, weights=weights_array)
        
        # 加权标准差
        weighted_variance = np.average((values_array - weighted_mean) ** 2, weights=weights_array)
        weighted_std = np.sqrt(weighted_variance)
        
        # 加权中位数
        weighted_median = self.calculate_weighted_percentile(values, weights, 0.5)
        
        return {
            'mean': float(weighted_mean),
            'std': float(weighted_std),
            'median': float(weighted_median)
        }
    
    def calculate_recent_smt_parameters(self, processed_data: List[Dict]) -> Dict[str, Any]:
        """计算近期S/M/T参数"""
        if not processed_data:
            return {}
        
        # 提取数据
        s_values = [record['emo_value'] for record in processed_data]
        m_values = [record['number'] for record in processed_data]
        weights = [record['effective_weight'] for record in processed_data]
        
        # 计算回复时间（简化处理，使用相邻记录时间差）
        t_values = []
        for i in range(1, len(processed_data)):
            time_diff = (processed_data[i]['create_time'] - processed_data[i-1]['create_time']).total_seconds()
            t_values.append(max(1, time_diff))  # 最小1秒
        
        # 如果只有一条记录，使用默认时间
        if not t_values:
            t_values = [300]  # 默认5分钟
        
        t_weights = weights[1:] if len(weights) > 1 else weights
        
        # S参数完整计算
        s_stats = {
            'P25': self.calculate_weighted_percentile(s_values, weights, 0.25),
            'P50': self.calculate_weighted_percentile(s_values, weights, 0.50),
            'P75': self.calculate_weighted_percentile(s_values, weights, 0.75),
            **self.calculate_weighted_stats(s_values, weights)
        }
        
        # M参数简化计算
        m_stats = self.calculate_weighted_stats(m_values, weights)
        
        # T参数优化计算
        t_stats = self.calculate_weighted_stats(t_values, t_weights)
        t_stats['median'] = self.calculate_weighted_percentile(t_values, t_weights, 0.5)
        
        return {
            'S_parameters': s_stats,
            'M_parameters': m_stats,
            'T_parameters': t_stats,
            'data_count': len(processed_data),
            'effective_weight_sum': sum(weights)
        }

    def calculate_type_matching_score(self, s_value: float, m_value: float, t_value: float,
                                    emotion_type: str) -> float:
        """计算单个情绪类型的匹配度"""
        features = self.emotion_type_features.get(emotion_type, {})

        # S匹配度计算
        s_range = features.get('S_range', (0, 10))
        if s_range[0] <= s_value <= s_range[1]:
            s_match = 1.0
        else:
            # 距离越远匹配度越低
            distance = min(abs(s_value - s_range[0]), abs(s_value - s_range[1]))
            s_match = max(0.0, 1.0 - distance / 5.0)

        # M匹配度计算
        m_range = features.get('M_range', (0, 100))
        if m_range[0] <= m_value <= m_range[1]:
            m_match = 1.0
        else:
            distance = min(abs(m_value - m_range[0]), abs(m_value - m_range[1]))
            m_match = max(0.0, 1.0 - distance / 50.0)

        # T匹配度计算
        t_range = features.get('T_median_range', (0, 3600))
        if t_range[0] <= t_value <= t_range[1]:
            t_match = 1.0
        else:
            distance = min(abs(t_value - t_range[0]), abs(t_value - t_range[1]))
            t_match = max(0.0, 1.0 - distance / 1800.0)

        # 加权综合匹配度
        total_score = (s_match * self.type_weights['S_weight'] +
                      m_match * self.type_weights['M_weight'] +
                      t_match * self.type_weights['T_weight'])

        return min(1.0, max(0.0, total_score))

    def calculate_emotion_type_scores(self, smt_params: Dict[str, Any]) -> Dict[str, Any]:
        """计算近期情绪类型得分"""
        if not smt_params:
            return {}

        # 提取关键参数
        s_p50 = smt_params['S_parameters']['P50']
        m_mean = smt_params['M_parameters']['mean']
        t_median = smt_params['T_parameters']['median']

        # 计算各类型匹配度
        type_scores = {}
        for emotion_type in self.emotion_type_features.keys():
            score = self.calculate_type_matching_score(s_p50, m_mean, t_median, emotion_type)
            type_scores[emotion_type] = score

        # 归一化处理
        total_score = sum(type_scores.values())
        if total_score > 0:
            type_scores = {k: v / total_score for k, v in type_scores.items()}

        # 识别主导类型
        dominant_type = max(type_scores, key=type_scores.get)
        dominant_score = type_scores[dominant_type]

        # 计算主导强度和类型确定性
        sorted_scores = sorted(type_scores.values(), reverse=True)
        dominant_strength = sorted_scores[0] - sorted_scores[1] if len(sorted_scores) > 1 else sorted_scores[0]
        type_certainty = dominant_score

        # 混合型判定
        if dominant_strength < 0.1:
            dominant_type = "混合型"

        return {
            'emotion_type_scores': type_scores,
            'dominant_emotion_type': dominant_type,
            'type_confidence': dominant_score,
            'dominant_strength': dominant_strength,
            'type_certainty': type_certainty
        }

    def calculate_confidence_metrics(self, processed_data: List[Dict], smt_params: Dict[str, Any],
                                   type_results: Dict[str, Any],
                                   long_term_profile: Dict[str, Any] = None,
                                   user_type: str = None,
                                   long_term_baseline: Dict[str, float] = None) -> Dict[str, float]:
        """计算多维度置信度"""
        data_count = len(processed_data)

        # 1. 数据充分性置信度
        data_sufficiency = min(1.0, data_count / 20)
        if data_count >= 20:
            sufficiency_level = 1.0
        elif data_count >= 10:
            sufficiency_level = 0.8
        elif data_count >= 5:
            sufficiency_level = 0.6
        else:
            sufficiency_level = 0.0

        data_sufficiency *= sufficiency_level

        # 2. 内部一致性置信度
        if smt_params:
            s_std = smt_params['S_parameters']['std']
            m_std = smt_params['M_parameters']['std']
            t_std = smt_params['T_parameters']['std']

            # 标准化方差计算
            s_cv = s_std / max(smt_params['S_parameters']['mean'], 1.0)
            m_cv = m_std / max(smt_params['M_parameters']['mean'], 1.0)
            t_cv = t_std / max(smt_params['T_parameters']['mean'], 1.0)

            variance_avg = (s_cv + m_cv + t_cv) / 3
            internal_consistency = max(0.0, min(1.0, 1.0 - variance_avg))
        else:
            internal_consistency = 0.0

        # 3. 时间稳定性置信度
        if processed_data:
            time_intervals = []
            for i in range(1, len(processed_data)):
                interval = (processed_data[i]['create_time'] - processed_data[i-1]['create_time']).total_seconds()
                time_intervals.append(interval)

            if time_intervals:
                time_std = np.std(time_intervals)
                time_mean = np.mean(time_intervals)
                time_skewness = abs(time_std / max(time_mean, 1.0))
                time_stability = max(0.0, min(1.0, 1.0 - time_skewness / 2))
            else:
                time_stability = 0.5
        else:
            time_stability = 0.0

        # 4. 与长期画像差异度（增强版）
        if user_type and type_results:
            # 使用直接提供的用户类型进行比较
            recent_type = type_results.get('dominant_emotion_type', '')

            if user_type == recent_type:
                type_consistency = 1.0
            else:
                # 基于类型得分差异计算
                recent_score = type_results.get('type_confidence', 0.5)
                type_consistency = max(0.0, min(1.0, 1.0 - (1.0 - recent_score)))
        elif long_term_profile and type_results:
            # 回退到原有逻辑
            long_term_type = long_term_profile.get('dominant_emotion_type', '')
            recent_type = type_results.get('dominant_emotion_type', '')

            if long_term_type == recent_type:
                type_consistency = 1.0
            else:
                long_term_score = long_term_profile.get('type_confidence', 0.5)
                recent_score = type_results.get('type_confidence', 0.5)
                difference = abs(recent_score - long_term_score)
                type_consistency = max(0.0, min(1.0, 1.0 - difference / 0.5))
        else:
            type_consistency = 0.5

        # 5. 基线一致性评估（新增）
        if long_term_baseline and smt_params:
            recent_baseline = smt_params.get('S_parameters', {})

            # 计算基线偏差
            p25_diff = abs(long_term_baseline.get('P25', 5.0) - recent_baseline.get('P25', 5.0))
            p50_diff = abs(long_term_baseline.get('P50', 6.0) - recent_baseline.get('P50', 6.0))
            p75_diff = abs(long_term_baseline.get('P75', 7.0) - recent_baseline.get('P75', 7.0))

            # 归一化偏差（假设最大合理偏差为2.0）
            baseline_deviation = (p25_diff + p50_diff + p75_diff) / 3.0
            baseline_consistency = max(0.0, min(1.0, 1.0 - baseline_deviation / 2.0))
        else:
            baseline_consistency = 0.5

        # 综合置信度计算（更新权重分配）
        comprehensive_confidence = (data_sufficiency * 0.35 +
                                  internal_consistency * 0.25 +
                                  time_stability * 0.15 +
                                  type_consistency * 0.15 +
                                  baseline_consistency * 0.1)

        return {
            'data_sufficiency': data_sufficiency,
            'internal_consistency': internal_consistency,
            'time_stability': time_stability,
            'type_consistency': type_consistency,
            'baseline_consistency': baseline_consistency,
            'comprehensive_confidence': comprehensive_confidence
        }

    def determine_confidence_level(self, confidence: float) -> str:
        """确定置信度等级"""
        if confidence >= 0.8:
            return "高"
        elif confidence >= 0.6:
            return "中"
        elif confidence >= 0.4:
            return "低"
        else:
            return "极低"

    def format_output_for_bayesian_update(self, type_results: Dict[str, Any],
                                        smt_params: Dict[str, Any],
                                        confidence_metrics: Dict[str, float],
                                        data_count: int) -> Dict[str, Any]:
        """格式化输出给1.4节贝叶斯更新的标准化数据"""

        # 确保数据质量
        observed_baseline = smt_params.get('S_parameters', {})
        p25 = observed_baseline.get('P25', 5.0)
        p50 = observed_baseline.get('P50', 6.0)
        p75 = observed_baseline.get('P75', 7.0)

        # 确保P25 ≤ P50 ≤ P75逻辑关系
        p25 = min(p25, p50)
        p75 = max(p75, p50)

        # 情绪类型得分归一化
        emotion_scores = type_results.get('emotion_type_scores', {})
        total_score = sum(emotion_scores.values())
        if total_score > 0:
            emotion_scores = {k: v / total_score for k, v in emotion_scores.items()}

        # 限制置信度值在[0,1]范围内
        type_confidence = max(0.0, min(1.0, type_results.get('type_confidence', 0.5)))
        analysis_confidence = max(0.0, min(1.0, confidence_metrics.get('comprehensive_confidence', 0.5)))

        return {
            "dominant_emotion_type": type_results.get('dominant_emotion_type', '适应调整型'),
            "type_confidence": round(type_confidence, 3),
            "emotion_type_scores": {k: round(v, 3) for k, v in emotion_scores.items()},
            "observed_baseline": {
                "P25": round(p25, 2),
                "P50": round(p50, 2),
                "P75": round(p75, 2)
            },
            "data_count": data_count,
            "analysis_confidence": round(analysis_confidence, 3)
        }

def validate_input_parameters(params: Dict[str, Any]) -> Dict[str, Any]:
    """验证输入参数"""
    validation_result = {
        'is_valid': True,
        'missing_params': [],
        'warnings': [],
        'debug_info': {}  # 添加调试信息
    }

    # 调试信息：记录输入参数的基本信息
    validation_result['debug_info']['params_type'] = str(type(params))
    validation_result['debug_info']['params_keys'] = list(params.keys()) if isinstance(params, dict) else []

    # 检查必需参数
    if 'working_memory' not in params:
        validation_result['missing_params'].append('working_memory')
        validation_result['is_valid'] = False
        validation_result['debug_info']['working_memory_exists'] = False
        return validation_result

    validation_result['debug_info']['working_memory_exists'] = True
    working_memory = params.get('working_memory', [])

    # 调试信息：记录working_memory的详细信息
    validation_result['debug_info']['working_memory_type'] = str(type(working_memory))
    validation_result['debug_info']['working_memory_length'] = len(working_memory) if working_memory else 0

    if not isinstance(working_memory, list):
        validation_result['warnings'].append('working_memory应该是数组类型')
        validation_result['is_valid'] = False
        return validation_result

    if len(working_memory) == 0:
        validation_result['warnings'].append('working_memory为空，无法进行近期画像分析')
        validation_result['is_valid'] = False
        return validation_result

    # 检查工作记忆数据结构
    required_fields = ['emo_value', 'number', 'bstudio_create_time']
    for i, record in enumerate(working_memory[:5]):  # 检查前5条
        for field in required_fields:
            if field not in record:
                validation_result['warnings'].append(f'working_memory[{i}]缺少字段: {field}')

    # 检查新增的重要参数
    if 'user_type' not in params:
        validation_result['warnings'].append('建议提供user_type以提升类型一致性评估')

    if not all(param in params for param in ['P25', 'P50', 'P75']):
        validation_result['warnings'].append('建议提供P25/P50/P75长期基线以提升基线一致性评估')

    # 检查可选参数
    if 'long_term_profile' not in params:
        validation_result['warnings'].append('建议提供long_term_profile以提升分析精度')

    if 'quality_weights' not in params:
        validation_result['warnings'].append('未提供quality_weights，将使用默认B级权重')

    if 'anomaly_flags' not in params:
        validation_result['warnings'].append('未提供anomaly_flags，将假设所有数据正常')

    if 'weight_anomaly' not in params:
        validation_result['warnings'].append('未提供weight_anomaly，将不进行全局权重调整')

    return validation_result

async def main(args: Args) -> Dict[str, Any]:
    """
    1.3 近期情绪画像建立主函数
    基于工作记忆层数据建立用户近期情绪画像
    """
    params = args.params

    try:
        # 输入参数验证
        validation = validate_input_parameters(params)

        if not validation['is_valid']:
            return {
                "calculation_id": "recent_emotion_profile",
                "version": "1.0.0",
                "timestamp": datetime.now().isoformat(),
                "error": True,
                "error_message": f"输入参数验证失败: {validation['missing_params']}",
                "validation_result": validation
            }

        # 初始化近期情绪画像建立器
        builder = RecentEmotionProfileBuilder()

        # 提取输入参数
        working_memory = params.get('working_memory', [])
        long_term_profile = params.get('long_term_profile', {})
        quality_weights = params.get('quality_weights', None)
        anomaly_flags = params.get('anomaly_flags', None)

        # 新增参数
        user_type = params.get('user_type', None)
        weight_anomaly = params.get('weight_anomaly', None)

        # 构建长期基线数据
        long_term_baseline = None
        if all(param in params for param in ['P25', 'P50', 'P75']):
            long_term_baseline = {
                'P25': builder.safe_float_convert(params.get('P25', '5.0')),
                'P50': builder.safe_float_convert(params.get('P50', '6.0')),
                'P75': builder.safe_float_convert(params.get('P75', '7.0'))
            }

        # 1. 数据验证与预处理（使用新增参数）
        processed_data, total_weight = builder.validate_and_preprocess_data(
            working_memory, quality_weights, anomaly_flags, weight_anomaly
        )

        if not processed_data:
            return {
                "calculation_id": "recent_emotion_profile",
                "version": "1.0.0",
                "timestamp": datetime.now().isoformat(),
                "error": True,
                "error_message": "工作记忆数据预处理后为空，无法进行分析",
                "data_count": 0,
                "fallback_recommendation": "建议延用长期画像或收集更多数据"
            }

        # 2. 计算近期S/M/T参数
        smt_params = builder.calculate_recent_smt_parameters(processed_data)

        # 3. 计算情绪类型得分
        type_results = builder.calculate_emotion_type_scores(smt_params)

        # 4. 计算置信度指标（使用新增参数）
        confidence_metrics = builder.calculate_confidence_metrics(
            processed_data, smt_params, type_results, long_term_profile, user_type, long_term_baseline
        )

        # 5. 格式化标准化输出
        standardized_output = builder.format_output_for_bayesian_update(
            type_results, smt_params, confidence_metrics, len(processed_data)
        )

        # 构建完整输出结果
        result = {
            # 系统信息
            "calculation_id": "recent_emotion_profile",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "algorithm_version": "recent_emotion_analysis_v1.3",

            # 标准化输出（给1.4节贝叶斯更新使用）
            **standardized_output,

            # 详细分析结果
            "detailed_analysis": {
                "smt_parameters": smt_params,
                "type_analysis": type_results,
                "confidence_breakdown": confidence_metrics,
                "confidence_level": builder.determine_confidence_level(
                    confidence_metrics.get('comprehensive_confidence', 0.0)
                )
            },

            # 数据处理信息
            "processing_info": {
                "raw_data_count": len(working_memory),
                "processed_data_count": len(processed_data),
                "total_effective_weight": round(total_weight, 3),
                "data_time_range": {
                    "start": processed_data[0]['create_time'].isoformat() if processed_data else None,
                    "end": processed_data[-1]['create_time'].isoformat() if processed_data else None
                },
                "enhanced_parameters": {
                    "user_type_provided": user_type is not None,
                    "long_term_baseline_provided": long_term_baseline is not None,
                    "weight_anomaly_applied": weight_anomaly is not None,
                    "user_type_value": user_type,
                    "weight_anomaly_level": weight_anomaly
                }
            },

            # 质量控制
            "quality_metrics": {
                "input_validation": validation,
                "data_sufficiency_level": "充足" if len(processed_data) >= 20 else
                                        "基本" if len(processed_data) >= 10 else
                                        "最少" if len(processed_data) >= 5 else "不足",
                "analysis_reliability": builder.determine_confidence_level(
                    confidence_metrics.get('comprehensive_confidence', 0.0)
                )
            }
        }

        return result

    except Exception as e:
        # 错误处理
        return {
            "calculation_id": "recent_emotion_profile",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "error": True,
            "error_message": str(e),
            "error_type": type(e).__name__,
            "fallback_recommendation": "建议检查输入数据格式或延用长期画像"
        }

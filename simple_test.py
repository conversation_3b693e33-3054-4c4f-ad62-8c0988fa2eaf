"""
简单测试近期情绪画像建立功能
"""

import asyncio
import json
from recent_emotion_profile_builder import main, Args

async def simple_test():
    # 简单测试数据
    working_memory = [
        {'emo_value': '7.0', 'number': '25', 'bstudio_create_time': '2024-07-15 10:00:00'},
        {'emo_value': '7.5', 'number': '30', 'bstudio_create_time': '2024-07-15 11:00:00'},
        {'emo_value': '6.8', 'number': '22', 'bstudio_create_time': '2024-07-15 12:00:00'},
        {'emo_value': '7.2', 'number': '28', 'bstudio_create_time': '2024-07-15 13:00:00'},
        {'emo_value': '6.9', 'number': '26', 'bstudio_create_time': '2024-07-15 14:00:00'}
    ]
    
    params = {'working_memory': working_memory}
    args = Args(params)
    result = await main(args)
    
    print('=== 近期情绪画像建立测试结果 ===')
    if result.get('error'):
        print(f'错误: {result.get("error_message")}')
    else:
        print(f'主导情绪类型: {result.get("dominant_emotion_type")}')
        print(f'类型置信度: {result.get("type_confidence")}')
        print(f'数据数量: {result.get("data_count")}')
        print(f'分析置信度: {result.get("analysis_confidence")}')
        
        # 显示观测基线
        baseline = result.get('observed_baseline', {})
        print(f'观测基线: P25={baseline.get("P25")}, P50={baseline.get("P50")}, P75={baseline.get("P75")}')
        
        # 显示各类型得分
        print('情绪类型得分:')
        for emotion_type, score in result.get('emotion_type_scores', {}).items():
            print(f'  {emotion_type}: {score:.3f}')
        
        print(f'数据充分性: {result["quality_metrics"]["data_sufficiency_level"]}')
        print(f'分析可靠性: {result["quality_metrics"]["analysis_reliability"]}')

if __name__ == "__main__":
    asyncio.run(simple_test())

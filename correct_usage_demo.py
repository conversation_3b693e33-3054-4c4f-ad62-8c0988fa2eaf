"""
1.3 近期情绪画像建立 - 正确使用示例
展示如何正确输入数据并获得P25/P50/P75等核心结果
"""

import asyncio
import json
from datetime import datetime, timedelta
from recent_emotion_profile_builder import main, Args

async def correct_usage_demo():
    """正确使用示例"""
    
    print("=" * 70)
    print("1.3 近期情绪画像建立 - 正确使用示例")
    print("=" * 70)
    
    # 🔥 关键：必须提供有效的working_memory数据
    print("\n【构造有效的working_memory数据】")
    
    # 模拟用户近期7天的交互数据
    base_time = datetime.now() - timedelta(days=7)
    working_memory = []
    
    # 生成20条真实的用户交互记录
    for i in range(20):
        # 时间递增
        record_time = base_time + timedelta(hours=i*8, minutes=i*10)
        
        # 模拟情绪分值变化（6.0-8.0范围，模拟乐观用户）
        emo_value = str(6.5 + (i % 5) * 0.3)  # 6.5, 6.8, 7.1, 7.4, 7.7循环
        
        # 模拟字数变化（15-40字范围）
        number = str(15 + (i % 8) * 3)  # 15, 18, 21, 24, 27, 30, 33, 36循环
        
        working_memory.append({
            'emo_value': emo_value,
            'number': number,
            'bstudio_create_time': record_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    print(f"✅ 构造了 {len(working_memory)} 条working_memory数据")
    print("前3条数据示例:")
    for i in range(3):
        record = working_memory[i]
        print(f"  {i+1}. 情绪值:{record['emo_value']}, 字数:{record['number']}, 时间:{record['bstudio_create_time']}")
    
    # 正确的参数配置
    print("\n【正确的参数配置】")
    correct_params = {
        # 🔥 必需参数：工作记忆数据
        'working_memory': working_memory,
        
        # 🆕 新增参数（提升精度）
        'user_type': '乐观开朗型',
        'P25': '6.5',
        'P50': '7.0', 
        'P75': '7.5',
        'weight_anomaly': 'normal'
    }
    
    print("参数配置:")
    print(f"  working_memory: {len(correct_params['working_memory'])} 条数据")
    print(f"  user_type: {correct_params['user_type']}")
    print(f"  长期基线: P25={correct_params['P25']}, P50={correct_params['P50']}, P75={correct_params['P75']}")
    
    # 执行正确的分析
    print("\n【执行近期情绪画像分析】")
    args = Args(correct_params)
    result = await main(args)
    
    # 检查是否成功
    if result.get('error'):
        print(f"❌ 分析失败: {result.get('error_message')}")
        print("验证结果:", result.get('validation_result'))
        return
    
    print("✅ 分析成功！")
    
    # 🎯 核心输出结果（这就是您要的）
    print("\n【🎯 核心输出结果（您要的P25/P50/P75等值）】")
    print(f"近期主导情绪类型: {result.get('dominant_emotion_type')}")
    print(f"类型置信度: {result.get('type_confidence'):.3f}")
    
    # 🔥 重点：这就是您要的P25/P50/P75值
    observed_baseline = result.get('observed_baseline', {})
    print(f"\n📊 近期情绪基线（核心结果）:")
    print(f"  P25 (25分位数): {observed_baseline.get('P25')}")
    print(f"  P50 (中位数): {observed_baseline.get('P50')}")  
    print(f"  P75 (75分位数): {observed_baseline.get('P75')}")
    
    # 情绪类型得分分布
    print(f"\n📈 情绪类型得分分布:")
    emotion_scores = result.get('emotion_type_scores', {})
    for emotion_type, score in emotion_scores.items():
        print(f"  {emotion_type}: {score:.3f}")
    
    # 分析质量指标
    print(f"\n📋 分析质量指标:")
    print(f"  数据数量: {result.get('data_count')}")
    print(f"  分析置信度: {result.get('analysis_confidence'):.3f}")
    
    quality_metrics = result.get('quality_metrics', {})
    print(f"  数据充分性: {quality_metrics.get('data_sufficiency_level')}")
    print(f"  分析可靠性: {quality_metrics.get('analysis_reliability')}")
    
    # 🎯 标准化输出（给后续模块使用）
    print(f"\n【🎯 标准化输出（给1.4节贝叶斯更新使用）】")
    standardized_output = {
        "dominant_emotion_type": result.get('dominant_emotion_type'),
        "type_confidence": result.get('type_confidence'),
        "emotion_type_scores": result.get('emotion_type_scores'),
        "observed_baseline": result.get('observed_baseline'),  # 🔥 这里包含P25/P50/P75
        "data_count": result.get('data_count'),
        "analysis_confidence": result.get('analysis_confidence')
    }
    
    print(json.dumps(standardized_output, indent=2, ensure_ascii=False))
    
    # 计算公式说明
    print(f"\n【💡 计算公式说明】")
    print("1. S参数计算（情绪分值统计）:")
    print("   - 从working_memory中提取所有emo_value")
    print("   - 计算加权分位数：P25, P50, P75")
    print("   - 考虑质量权重和异常标记")
    
    print("2. M参数计算（字数统计）:")
    print("   - 从working_memory中提取所有number")
    print("   - 计算加权平均值和标准差")
    
    print("3. T参数计算（时间间隔统计）:")
    print("   - 基于bstudio_create_time计算相邻记录时间差")
    print("   - 计算时间间隔的统计量")
    
    print("4. 情绪类型匹配:")
    print("   - 基于S/M/T参数匹配五大情绪类型")
    print("   - 计算各类型的匹配度得分")
    
    print("5. 置信度评估:")
    print("   - 数据充分性、内部一致性、时间稳定性")
    print("   - 类型一致性、基线一致性（新增）")
    
    print(f"\n【✅ 总结】")
    print("🎯 核心输出就是:")
    print(f"  - 近期主导情绪类型: {result.get('dominant_emotion_type')}")
    print(f"  - P25基线值: {observed_baseline.get('P25')}")
    print(f"  - P50基线值: {observed_baseline.get('P50')}")
    print(f"  - P75基线值: {observed_baseline.get('P75')}")
    print(f"  - 分析置信度: {result.get('analysis_confidence'):.3f}")
    
    print("\n🔥 关键：必须提供有效的working_memory数据才能得到这些结果！")

if __name__ == "__main__":
    asyncio.run(correct_usage_demo())
